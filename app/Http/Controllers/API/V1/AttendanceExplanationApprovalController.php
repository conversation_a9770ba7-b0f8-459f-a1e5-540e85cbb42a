<?php

namespace App\Http\Controllers\API\V1;

use App\Http\Controllers\Controller;
use App\Models\AttendanceExplanation;
use App\Services\AttendanceExplanation\AttendanceExplanationService;
use Illuminate\Http\Request;

class AttendanceExplanationApprovalController extends Controller
{
    protected AttendanceExplanationService $attendanceExplanationService;

    public function __construct(AttendanceExplanationService $attendanceExplanationService)
    {
        $this->attendanceExplanationService = $attendanceExplanationService;
    }

    /**
     * L<PERSON><PERSON> danh sách giải trình cần manager duyệt
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getManagerPendingExplanations(Request $request)
    {
        try {
            $user = auth()->user();

            // Lấy danh sách giải trình cần manager duyệt (pending)
            $pendingExplanations = AttendanceExplanation::whereHas('user', function($query) use ($user) {
                    $query->whereHas('managers', function($subQuery) use ($user) {
                        $subQuery->where('user_managers.manager_user_id', $user->id)
                                 ->whereNull('user_managers.deleted_at');
                    });
                })
                ->with(['user.staffDepartment', 'managerApprover', 'hrApprover', 'remoteShift', 'taggedUser'])
                ->byManagerStatus('pending')
                ->orderBy('attendance_explanations.created_at', 'desc')
                ->get();

            // Format data
            $formattedExplanations = $pendingExplanations->map(function ($explanation) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->format('Y-m-d'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'ot_hours' => $explanation->ot_hours,
                    'remote_shift' => $explanation->remoteShift ? [
                        'id' => $explanation->remoteShift->id,
                        'name' => $explanation->remoteShift->name,
                    ] : null,
                    'final_status' => $explanation->final_status,
                    'manager_status' => $explanation->manager_status,
                    'hr_status' => $explanation->hr_status,
                    'tagged_user_status' => $explanation->tagged_user_status,
                    'tagged_user' => $explanation->taggedUser ? [
                        'id' => $explanation->taggedUser->id,
                        'name' => $explanation->taggedUser->name,
                    ] : null,
                    'user' => [
                        'id' => $explanation->user->id,
                        'name' => $explanation->user->name,
                        'account' => $explanation->user->account,
                        'department_name' => $explanation->user->staffDepartment ? $explanation->user->staffDepartment->name : null,
                    ],
                    'created_at' => $explanation->created_at->format('Y-m-d H:i:s'),
                ];
            });

            // Nhóm theo user
            $groupedByUser = $formattedExplanations->groupBy('user.id')->map(function ($explanations, $userId) {
                $user = $explanations->first()['user'];
                return [
                    'user' => $user,
                    'explanations' => $explanations->values(),
                    'count' => $explanations->count(),
                ];
            })->values();

            return response()->json([
                'success' => true,
                'data' => [
                    'pending_explanations' => $formattedExplanations,
                    'grouped_by_user' => $groupedByUser,
                    'stats' => [
                        'total_pending' => $formattedExplanations->count(),
                        'users_with_pending' => $groupedByUser->count(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy lịch sử duyệt của manager
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getManagerHistory(Request $request)
    {
        try {
            $user = auth()->user();

            $query = AttendanceExplanation::whereHas('user', function($query) use ($user) {
                    $query->whereHas('managers', function($subQuery) use ($user) {
                        $subQuery->where('user_managers.manager_user_id', $user->id)
                                 ->whereNull('user_managers.deleted_at');
                    });
                })
                ->with(['user.staffDepartment', 'managerApprover', 'hrApprover'])
                ->whereIn('manager_status', ['approved', 'rejected'])
                ->whereNotNull('manager_approved_at');

            // Filter by date range if provided
            if ($request->has('from_date')) {
                $query->where('manager_approved_at', '>=', $request->from_date);
            }
            if ($request->has('to_date')) {
                $query->where('manager_approved_at', '<=', $request->to_date . ' 23:59:59');
            }

            // Default to last month if no date filter
            if (!$request->has('from_date') && !$request->has('to_date')) {
                $query->where('manager_approved_at', '>=', now()->subMonth());
            }

            $historyExplanations = $query->orderBy('attendance_explanations.manager_approved_at', 'desc')->get();

            // Format data
            $formattedHistory = $historyExplanations->map(function ($explanation) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->format('Y-m-d'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'manager_status' => $explanation->manager_status,
                    'manager_approved_at' => $explanation->manager_approved_at ? $explanation->manager_approved_at->format('Y-m-d H:i:s') : null,
                    'manager_note' => $explanation->manager_note,
                    'hr_status' => $explanation->hr_status,
                    'final_status' => $explanation->final_status,
                    'user' => [
                        'id' => $explanation->user->id,
                        'name' => $explanation->user->name,
                        'account' => $explanation->user->account,
                        'department_name' => $explanation->user->staffDepartment ? $explanation->user->staffDepartment->name : null,
                    ],
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'history_explanations' => $formattedHistory,
                    'stats' => [
                        'total_processed' => $formattedHistory->count(),
                        'approved_count' => $formattedHistory->where('manager_status', 'approved')->count(),
                        'rejected_count' => $formattedHistory->where('manager_status', 'rejected')->count(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy danh sách user đang được quản lý
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getManagedUsers(Request $request)
    {
        try {
            $user = auth()->user();

            // Lấy danh sách user được quản lý
            $managedUsers = \App\Models\User::whereHas('managers', function($query) use ($user) {
                    $query->where('user_managers.manager_user_id', $user->id)
                          ->whereNull('user_managers.deleted_at');
                })
                ->with(['staffDepartment'])
                ->where('active_flag', true)
                ->whereNull('deleted_at')
                ->get();

            // Đếm số giải trình pending cho mỗi user
            $usersWithStats = $managedUsers->map(function($managedUser) {
                $pendingCount = AttendanceExplanation::where('user_id', $managedUser->id)
                    ->where('manager_status', 'pending')
                    ->count();

                return [
                    'id' => $managedUser->id,
                    'name' => $managedUser->name,
                    'account' => $managedUser->account,
                    'department_name' => $managedUser->staffDepartment ? $managedUser->staffDepartment->name : null,
                    'phone_number' => substr($managedUser->phone_number, -4), // Chỉ hiển thị 4 số cuối
                    'pending_explanations_count' => $pendingCount,
                ];
            });

            // Tính tổng số giải trình pending
            $totalPendingExplanations = $usersWithStats->sum('pending_explanations_count');

            return response()->json([
                'success' => true,
                'data' => [
                    'managed_users' => $usersWithStats,
                    'total_users' => $usersWithStats->count(),
                    'total_pending_explanations' => $totalPendingExplanations,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Manager duyệt giải trình
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function managerApprove(Request $request, $id)
    {
        try {
            $user = auth()->user();

            $request->validate([
                'action' => 'required|in:approve,reject',
                'note' => 'nullable|string|max:500',
            ]);

            $explanation = AttendanceExplanation::with(['user'])
                ->whereHas('user', function($query) use ($user) {
                    $query->whereHas('managers', function($subQuery) use ($user) {
                        $subQuery->where('user_managers.manager_user_id', $user->id)
                                 ->whereNull('user_managers.deleted_at');
                    });
                })
                ->where('id', $id)
                ->first();

            if (!$explanation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy giải trình hoặc bạn không có quyền duyệt'
                ], 404);
            }

            if ($explanation->manager_status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Giải trình này đã được xử lý trước đó'
                ], 400);
            }

            $result = $this->attendanceExplanationService->managerApproveExplanation(
                $user,
                $explanation,
                $request->action === 'approve',
                $request->note
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'id' => $explanation->id,
                        'manager_status' => $explanation->fresh()->manager_status,
                        'final_status' => $explanation->fresh()->final_status,
                    ]
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 400);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Manager bulk approve/reject explanations
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function managerBulkApprove(Request $request)
    {
        try {
            $user = auth()->user();

            $request->validate([
                'explanation_ids' => 'required|array|min:1|max:50', // Giới hạn tối đa 50 items
                'explanation_ids.*' => 'required|integer|exists:attendance_explanations,id',
                'action' => 'required|in:approve,reject,approved,rejected',
                'note' => 'nullable|string|max:1000'
            ]);

            $explanationIds = $request->explanation_ids;
            $action = $request->action;
            $note = $request->note;

            // Lấy các giải trình cần duyệt và kiểm tra quyền
            $explanations = AttendanceExplanation::with(['user'])
                ->whereHas('user', function($query) use ($user) {
                    $query->whereHas('managers', function($subQuery) use ($user) {
                        $subQuery->where('user_managers.manager_user_id', $user->id)
                                 ->whereNull('user_managers.deleted_at');
                    });
                })
                ->whereIn('id', $explanationIds)
                ->where('manager_status', 'pending')
                ->get();

            if ($explanations->count() !== count($explanationIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Một số giải trình không tồn tại, đã được xử lý hoặc bạn không có quyền duyệt'
                ], 400);
            }

            $processedCount = 0;
            $failedCount = 0;
            $results = [];

            \DB::beginTransaction();

            foreach ($explanations as $explanation) {
                try {
                    $result = $this->attendanceExplanationService->managerApproveExplanation(
                        $user,
                        $explanation,
                        in_array($action, ['approve', 'approved']),
                        $note
                    );

                    if ($result['success']) {
                        $processedCount++;
                        $results[] = [
                            'id' => $explanation->id,
                            'user_name' => $explanation->user->name,
                            'date' => $explanation->date->format('d/m/Y'),
                            'status' => 'success',
                            'message' => $result['message']
                        ];
                    } else {
                        $failedCount++;
                        $results[] = [
                            'id' => $explanation->id,
                            'user_name' => $explanation->user->name,
                            'date' => $explanation->date->format('d/m/Y'),
                            'status' => 'failed',
                            'message' => $result['message']
                        ];
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    $results[] = [
                        'id' => $explanation->id,
                        'user_name' => $explanation->user->name,
                        'date' => $explanation->date->format('d/m/Y'),
                        'status' => 'failed',
                        'message' => 'Lỗi xử lý: ' . $e->getMessage()
                    ];
                }
            }

            \DB::commit();

            $actionText = in_array($action, ['approve', 'approved']) ? 'duyệt' : 'từ chối';
            $message = "Đã {$actionText} thành công {$processedCount} giải trình";

            if ($failedCount > 0) {
                $message .= ", {$failedCount} giải trình thất bại";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'processed_count' => $processedCount,
                    'failed_count' => $failedCount,
                    'total_count' => count($explanationIds),
                    'results' => $results
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            \DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy giải trình chưa duyệt của một user cụ thể
     *
     * @param Request $request
     * @param int $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserPendingExplanations(Request $request, $userId)
    {
        try {
            $user = auth()->user();

            // Kiểm tra quyền xem giải trình của user này
            $canView = \DB::table('user_managers')
                ->where('user_id', $userId)
                ->where('manager_user_id', $user->id)
                ->whereNull('deleted_at')
                ->exists();

            if (!$canView) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền xem giải trình của user này'
                ], 403);
            }

            // Lấy user info
            $targetUser = \App\Models\User::with('staffDepartment')->find($userId);
            if (!$targetUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy user'
                ], 404);
            }

            // Lấy giải trình chưa duyệt
            $explanations = AttendanceExplanation::where('user_id', $userId)
                ->where('manager_status', 'pending')
                ->with(['remoteShift', 'taggedUser'])
                ->orderBy('created_at', 'desc')
                ->get();

            $formattedExplanations = $explanations->map(function ($explanation) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->format('Y-m-d'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'ot_hours' => $explanation->ot_hours,
                    'remote_shift' => $explanation->remoteShift ? [
                        'id' => $explanation->remoteShift->id,
                        'name' => $explanation->remoteShift->name,
                    ] : null,
                    'final_status' => $explanation->final_status,
                    'manager_status' => $explanation->manager_status,
                    'hr_status' => $explanation->hr_status,
                    'tagged_user_status' => $explanation->tagged_user_status,
                    'tagged_user' => $explanation->taggedUser ? [
                        'id' => $explanation->taggedUser->id,
                        'name' => $explanation->taggedUser->name,
                    ] : null,
                    'created_at' => $explanation->created_at->format('Y-m-d H:i:s'),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => [
                        'id' => $targetUser->id,
                        'name' => $targetUser->name,
                        'account' => $targetUser->account,
                        'department_name' => $targetUser->staffDepartment ? $targetUser->staffDepartment->name : null,
                    ],
                    'pending_explanations' => $formattedExplanations,
                    'stats' => [
                        'total_pending' => $formattedExplanations->count(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy giải trình đã duyệt của một user cụ thể
     *
     * @param Request $request
     * @param int $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserApprovedExplanations(Request $request, $userId)
    {
        try {
            $user = auth()->user();

            // Kiểm tra quyền xem giải trình của user này
            $canView = \DB::table('user_managers')
                ->where('user_id', $userId)
                ->where('manager_user_id', $user->id)
                ->whereNull('deleted_at')
                ->exists();

            if (!$canView) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền xem giải trình của user này'
                ], 403);
            }

            // Lấy user info
            $targetUser = \App\Models\User::with('staffDepartment')->find($userId);
            if (!$targetUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy user'
                ], 404);
            }

            // Query builder cho giải trình đã duyệt
            $query = AttendanceExplanation::where('user_id', $userId)
                ->whereIn('manager_status', ['approved', 'rejected'])
                ->with(['remoteShift', 'taggedUser', 'managerApprover', 'hrApprover']);

            // Filter by date range if provided
            if ($request->has('from_date')) {
                $query->where('manager_approved_at', '>=', $request->from_date);
            }
            if ($request->has('to_date')) {
                $query->where('manager_approved_at', '<=', $request->to_date . ' 23:59:59');
            }

            // Filter by status if provided
            if ($request->has('status') && in_array($request->status, ['approved', 'rejected'])) {
                $query->where('manager_status', $request->status);
            }

            // Default to last 3 months if no date filter
            if (!$request->has('from_date') && !$request->has('to_date')) {
                $query->where('manager_approved_at', '>=', now()->subMonths(3));
            }

            $explanations = $query->orderBy('manager_approved_at', 'desc')
                ->limit(100) // Giới hạn 100 bản ghi
                ->get();

            $formattedExplanations = $explanations->map(function ($explanation) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->format('Y-m-d'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'ot_hours' => $explanation->ot_hours,
                    'remote_shift' => $explanation->remoteShift ? [
                        'id' => $explanation->remoteShift->id,
                        'name' => $explanation->remoteShift->name,
                    ] : null,
                    'final_status' => $explanation->final_status,
                    'manager_status' => $explanation->manager_status,
                    'hr_status' => $explanation->hr_status,
                    'manager_approved_at' => $explanation->manager_approved_at ? $explanation->manager_approved_at->format('Y-m-d H:i:s') : null,
                    'hr_approved_at' => $explanation->hr_approved_at ? $explanation->hr_approved_at->format('Y-m-d H:i:s') : null,
                    'manager_note' => $explanation->manager_note,
                    'hr_note' => $explanation->hr_note,
                    'tagged_user_status' => $explanation->tagged_user_status,
                    'tagged_user' => $explanation->taggedUser ? [
                        'id' => $explanation->taggedUser->id,
                        'name' => $explanation->taggedUser->name,
                    ] : null,
                    'manager_approver' => $explanation->managerApprover ? [
                        'id' => $explanation->managerApprover->id,
                        'name' => $explanation->managerApprover->name,
                    ] : null,
                    'hr_approver' => $explanation->hrApprover ? [
                        'id' => $explanation->hrApprover->id,
                        'name' => $explanation->hrApprover->name,
                    ] : null,
                    'created_at' => $explanation->created_at->format('Y-m-d H:i:s'),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => [
                        'id' => $targetUser->id,
                        'name' => $targetUser->name,
                        'account' => $targetUser->account,
                        'department_name' => $targetUser->staffDepartment ? $targetUser->staffDepartment->name : null,
                    ],
                    'approved_explanations' => $formattedExplanations,
                    'stats' => [
                        'total_processed' => $formattedExplanations->count(),
                        'approved_count' => $formattedExplanations->where('manager_status', 'approved')->count(),
                        'rejected_count' => $formattedExplanations->where('manager_status', 'rejected')->count(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy tất cả giải trình của một user (chưa duyệt + đã duyệt)
     *
     * @param Request $request
     * @param int $userId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserExplanations(Request $request, $userId)
    {
        try {
            $user = auth()->user();

            // Kiểm tra quyền xem giải trình của user này
            $canView = \DB::table('user_managers')
                ->where('user_id', $userId)
                ->where('manager_user_id', $user->id)
                ->whereNull('deleted_at')
                ->exists();

            if (!$canView) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền xem giải trình của user này'
                ], 403);
            }

            // Lấy user info
            $targetUser = \App\Models\User::with('staffDepartment')->find($userId);
            if (!$targetUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy user'
                ], 404);
            }

            // Query builder với filter options
            $query = AttendanceExplanation::where('user_id', $userId)
                ->with(['remoteShift', 'taggedUser', 'managerApprover', 'hrApprover']);

            // Filter by date range if provided
            if ($request->has('from_date')) {
                $query->where('date', '>=', $request->from_date);
            }
            if ($request->has('to_date')) {
                $query->where('date', '<=', $request->to_date);
            }

            // Filter by manager status if provided
            if ($request->has('manager_status') && in_array($request->manager_status, ['pending', 'approved', 'rejected'])) {
                $query->where('manager_status', $request->manager_status);
            }

            // Filter by final status if provided
            if ($request->has('final_status')) {
                $query->where('final_status', $request->final_status);
            }

            // Default to last 3 months if no date filter
            if (!$request->has('from_date') && !$request->has('to_date')) {
                $query->where('date', '>=', now()->subMonths(3));
            }

            $explanations = $query->orderBy('date', 'desc')
                ->orderBy('created_at', 'desc')
                ->limit(100) // Giới hạn 100 bản ghi
                ->get();

            // Phân loại giải trình
            $pendingExplanations = $explanations->where('manager_status', 'pending');
            $approvedExplanations = $explanations->whereIn('manager_status', ['approved', 'rejected']);

            // Format data
            $formatExplanation = function ($explanation) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->format('Y-m-d'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'ot_hours' => $explanation->ot_hours,
                    'remote_shift' => $explanation->remoteShift ? [
                        'id' => $explanation->remoteShift->id,
                        'name' => $explanation->remoteShift->name,
                    ] : null,
                    'final_status' => $explanation->final_status,
                    'manager_status' => $explanation->manager_status,
                    'hr_status' => $explanation->hr_status,
                    'manager_approved_at' => $explanation->manager_approved_at ? $explanation->manager_approved_at->format('Y-m-d H:i:s') : null,
                    'hr_approved_at' => $explanation->hr_approved_at ? $explanation->hr_approved_at->format('Y-m-d H:i:s') : null,
                    'manager_note' => $explanation->manager_note,
                    'hr_note' => $explanation->hr_note,
                    'tagged_user_status' => $explanation->tagged_user_status,
                    'tagged_user' => $explanation->taggedUser ? [
                        'id' => $explanation->taggedUser->id,
                        'name' => $explanation->taggedUser->name,
                    ] : null,
                    'manager_approver' => $explanation->managerApprover ? [
                        'id' => $explanation->managerApprover->id,
                        'name' => $explanation->managerApprover->name,
                    ] : null,
                    'hr_approver' => $explanation->hrApprover ? [
                        'id' => $explanation->hrApprover->id,
                        'name' => $explanation->hrApprover->name,
                    ] : null,
                    'created_at' => $explanation->created_at->format('Y-m-d H:i:s'),
                ];
            };

            $formattedPending = $pendingExplanations->map($formatExplanation)->values();
            $formattedApproved = $approvedExplanations->map($formatExplanation)->values();
            $allFormatted = $explanations->map($formatExplanation);

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => [
                        'id' => $targetUser->id,
                        'name' => $targetUser->name,
                        'account' => $targetUser->account,
                        'department_name' => $targetUser->staffDepartment ? $targetUser->staffDepartment->name : null,
                    ],
                    'all_explanations' => $allFormatted,
                    'pending_explanations' => $formattedPending,
                    'approved_explanations' => $formattedApproved,
                    'stats' => [
                        'total' => $explanations->count(),
                        'pending_count' => $pendingExplanations->count(),
                        'approved_count' => $approvedExplanations->where('manager_status', 'approved')->count(),
                        'rejected_count' => $approvedExplanations->where('manager_status', 'rejected')->count(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy danh sách giải trình cần HR duyệt
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHrPendingExplanations(Request $request)
    {
        try {
            $user = auth()->user();

            // Kiểm tra quyền HR (chỉ role R080)
            if (!$user->hasRole('R080')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền truy cập chức năng này'
                ], 403);
            }

            // Lấy danh sách giải trình cần HR duyệt (manager đã duyệt, HR chưa duyệt)
            $pendingExplanations = AttendanceExplanation::with(['user.staffDepartment', 'managerApprover', 'hrApprover', 'remoteShift', 'taggedUser'])
                ->byManagerStatus('approved')
                ->byHrStatus('pending')
                ->orderBy('attendance_explanations.manager_approved_at', 'desc')
                ->get();

            $formattedExplanations = $pendingExplanations->map(function ($explanation) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->format('Y-m-d'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'ot_hours' => $explanation->ot_hours,
                    'remote_shift' => $explanation->remoteShift ? [
                        'id' => $explanation->remoteShift->id,
                        'name' => $explanation->remoteShift->name,
                    ] : null,
                    'final_status' => $explanation->final_status,
                    'manager_status' => $explanation->manager_status,
                    'hr_status' => $explanation->hr_status,
                    'manager_approved_at' => $explanation->manager_approved_at ? $explanation->manager_approved_at->format('Y-m-d H:i:s') : null,
                    'manager_note' => $explanation->manager_note,
                    'tagged_user_status' => $explanation->tagged_user_status,
                    'tagged_user' => $explanation->taggedUser ? [
                        'id' => $explanation->taggedUser->id,
                        'name' => $explanation->taggedUser->name,
                    ] : null,
                    'user' => [
                        'id' => $explanation->user->id,
                        'name' => $explanation->user->name,
                        'account' => $explanation->user->account,
                        'department_name' => $explanation->user->staffDepartment ? $explanation->user->staffDepartment->name : null,
                    ],
                    'manager_approver' => $explanation->managerApprover ? [
                        'id' => $explanation->managerApprover->id,
                        'name' => $explanation->managerApprover->name,
                    ] : null,
                    'created_at' => $explanation->created_at->format('Y-m-d H:i:s'),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'pending_explanations' => $formattedExplanations,
                    'stats' => [
                        'total_pending' => $formattedExplanations->count(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Lấy lịch sử duyệt của HR
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHrHistory(Request $request)
    {
        try {
            $user = auth()->user();

            // Kiểm tra quyền HR (chỉ role R080)
            if (!$user->hasRole('R080')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền truy cập chức năng này'
                ], 403);
            }

            $query = AttendanceExplanation::with(['user.staffDepartment', 'managerApprover', 'hrApprover'])
                ->whereIn('hr_status', ['approved', 'rejected'])
                ->whereNotNull('hr_approved_at');

            // Filter by date range if provided
            if ($request->has('from_date')) {
                $query->where('hr_approved_at', '>=', $request->from_date);
            }
            if ($request->has('to_date')) {
                $query->where('hr_approved_at', '<=', $request->to_date . ' 23:59:59');
            }

            // Default to last month if no date filter
            if (!$request->has('from_date') && !$request->has('to_date')) {
                $query->where('hr_approved_at', '>=', now()->subMonth());
            }

            $historyExplanations = $query->orderBy('attendance_explanations.hr_approved_at', 'desc')->get();

            $formattedHistory = $historyExplanations->map(function ($explanation) {
                return [
                    'id' => $explanation->id,
                    'date' => $explanation->date->format('Y-m-d'),
                    'explanation' => $explanation->explanation,
                    'explanation_type' => $explanation->explanation_type,
                    'explanation_type_text' => $explanation->explanation_type_text,
                    'manager_status' => $explanation->manager_status,
                    'hr_status' => $explanation->hr_status,
                    'hr_approved_at' => $explanation->hr_approved_at ? $explanation->hr_approved_at->format('Y-m-d H:i:s') : null,
                    'hr_note' => $explanation->hr_note,
                    'final_status' => $explanation->final_status,
                    'user' => [
                        'id' => $explanation->user->id,
                        'name' => $explanation->user->name,
                        'account' => $explanation->user->account,
                        'department_name' => $explanation->user->staffDepartment ? $explanation->user->staffDepartment->name : null,
                    ],
                    'manager_approver' => $explanation->managerApprover ? [
                        'id' => $explanation->managerApprover->id,
                        'name' => $explanation->managerApprover->name,
                    ] : null,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'history_explanations' => $formattedHistory,
                    'stats' => [
                        'total_processed' => $formattedHistory->count(),
                        'approved_count' => $formattedHistory->where('hr_status', 'approved')->count(),
                        'rejected_count' => $formattedHistory->where('hr_status', 'rejected')->count(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * HR duyệt giải trình
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function hrApprove(Request $request, $id)
    {
        try {
            $user = auth()->user();

            // Kiểm tra quyền HR (chỉ role R080)
            if (!$user->hasRole('R080')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền truy cập chức năng này'
                ], 403);
            }

            $request->validate([
                'action' => 'required|in:approve,reject',
                'note' => 'nullable|string|max:500',
            ]);

            $explanation = AttendanceExplanation::with(['user'])
                ->where('id', $id)
                ->first();

            if (!$explanation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy giải trình'
                ], 404);
            }

            if ($explanation->manager_status !== 'approved') {
                return response()->json([
                    'success' => false,
                    'message' => 'Giải trình này chưa được manager duyệt'
                ], 400);
            }

            if ($explanation->hr_status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Giải trình này đã được HR xử lý trước đó'
                ], 400);
            }

            $result = $this->attendanceExplanationService->hrApproveExplanation(
                $user,
                $explanation,
                $request->action === 'approve',
                $request->note
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'id' => $explanation->id,
                        'hr_status' => $explanation->fresh()->hr_status,
                        'final_status' => $explanation->fresh()->final_status,
                    ]
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 400);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * HR bulk approve/reject explanations
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function hrBulkApprove(Request $request)
    {
        try {
            $user = auth()->user();

            // Kiểm tra quyền HR (chỉ role R080)
            if (!$user->hasRole('R080')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền truy cập chức năng này'
                ], 403);
            }

            $request->validate([
                'explanation_ids' => 'required|array|min:1|max:50', // Giới hạn tối đa 50 items
                'explanation_ids.*' => 'required|integer|exists:attendance_explanations,id',
                'action' => 'required|in:approve,reject,approved,rejected',
                'note' => 'nullable|string|max:1000'
            ]);

            $explanationIds = $request->explanation_ids;
            $action = $request->action;
            $note = $request->note;

            // Lấy các giải trình cần HR duyệt
            $explanations = AttendanceExplanation::with(['user'])
                ->whereIn('id', $explanationIds)
                ->where('manager_status', 'approved')
                ->where('hr_status', 'pending')
                ->get();

            if ($explanations->count() !== count($explanationIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Một số giải trình không tồn tại, chưa được manager duyệt hoặc đã được HR xử lý'
                ], 400);
            }

            $processedCount = 0;
            $failedCount = 0;
            $results = [];

            \DB::beginTransaction();

            foreach ($explanations as $explanation) {
                try {
                    $result = $this->attendanceExplanationService->hrApproveExplanation(
                        $user,
                        $explanation,
                        in_array($action, ['approve', 'approved']),
                        $note
                    );

                    if ($result['success']) {
                        $processedCount++;
                        $results[] = [
                            'id' => $explanation->id,
                            'user_name' => $explanation->user->name,
                            'date' => $explanation->date->format('d/m/Y'),
                            'status' => 'success',
                            'message' => $result['message']
                        ];
                    } else {
                        $failedCount++;
                        $results[] = [
                            'id' => $explanation->id,
                            'user_name' => $explanation->user->name,
                            'date' => $explanation->date->format('d/m/Y'),
                            'status' => 'failed',
                            'message' => $result['message']
                        ];
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    $results[] = [
                        'id' => $explanation->id,
                        'user_name' => $explanation->user->name,
                        'date' => $explanation->date->format('d/m/Y'),
                        'status' => 'failed',
                        'message' => 'Lỗi xử lý: ' . $e->getMessage()
                    ];
                }
            }

            \DB::commit();

            $actionText = in_array($action, ['approve', 'approved']) ? 'duyệt' : 'từ chối';
            $message = "Đã {$actionText} thành công {$processedCount} giải trình";

            if ($failedCount > 0) {
                $message .= ", {$failedCount} giải trình thất bại";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'processed_count' => $processedCount,
                    'failed_count' => $failedCount,
                    'total_count' => count($explanationIds),
                    'results' => $results
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            \DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }
}
