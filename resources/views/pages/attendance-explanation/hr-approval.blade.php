@extends('layouts.authenticated')

@section('page.title', '<PERSON><PERSON><PERSON><PERSON> gi<PERSON>i trình chấm công - HR')

@section('page.styles')
<style>
/* Department Card Styles */
.department-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.department-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.department-header {
    padding: 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px 8px 0 0;
}

.department-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.department-avatar {
    margin-right: 15px;
}

.department-avatar .avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    font-weight: bold;
}

.department-details {
    flex: 1;
}

.department-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.department-meta {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.department-staff, .department-count {
    color: #6c757d;
    font-size: 14px;
}

.department-stats {
    display: flex;
    align-items: center;
    gap: 15px;
}

.hr-pending-count {
    text-align: center;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 8px 12px;
}

.hr-pending-count .count-number {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #856404;
}

.hr-pending-count .count-label {
    display: block;
    font-size: 12px;
    color: #856404;
}

.expand-icon {
    color: #6c757d;
    font-size: 16px;
}

/* User Card Styles */
.user-card {
    margin: 10px 0;
    border: 1px solid #e3f2fd;
    border-radius: 6px;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.user-card:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.user-header {
    cursor: pointer;
    padding: 10px 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    border-left: 4px solid #17a2b8;
    border-radius: 6px 6px 0 0;
    transition: background 0.3s ease;
}

.user-header:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.user-header h6 {
    margin: 0;
    font-weight: 600;
}

.user-header .text-info {
    color: #17a2b8 !important;
}

/* Select User Button */
.select-user-btn {
    font-size: 11px;
    border-radius: 3px;
    transition: all 0.3s ease;
    border: 1px solid #17a2b8;
    background: #17a2b8;
    color: white;
}

.select-user-btn:hover {
    background: #138496;
    border-color: #138496;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* History Stats */
.hr-history-stats {
    display: flex;
    gap: 8px;
    align-items: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.stat-item.approved {
    background: #d4edda;
    color: #155724;
}

.stat-item.rejected {
    background: #f8d7da;
    color: #721c24;
}

.stat-number {
    font-weight: bold;
    font-size: 14px;
}

.stat-label {
    font-size: 10px;
}

/* Improved spacing and animations */
.department-card, .user-card {
    transition: all 0.3s ease;
}

.department-card:hover, .user-card:hover {
    transform: translateY(-2px);
}

/* Better responsive design */
@media (max-width: 768px) {
    .department-meta, .user-header .row {
        flex-direction: column;
        gap: 5px;
    }

    .select-user-btn {
        margin-bottom: 5px;
    }

    .department-stats, .employee-stats {
        margin-top: 10px;
    }
}
</style>
@endsection

@section('page.breadcrumb')
    <ol class="breadcrumb">
        <li><a href="{{ route('auth.home') }}"><i class="fa fa-dashboard"></i> Trang chủ</a></li>
        <li><a href="{{ route('attendance-explanation.index') }}">Giải trình chấm công</a></li>
        <li class="active">Duyệt giải trình (HR)</li>
    </ol>
@endsection

@section('page.content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-building"></i> Duyệt giải trình chấm công - HCNS
                        <span class="badge badge-warning pull-right">{{ $stats['pending_count'] }} đơn chờ duyệt</span>
                    </h3>
                </div>
                <div class="panel-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 20px;">
                        <li role="presentation" class="active">
                            <a href="#pending-tab" aria-controls="pending-tab" role="tab" data-toggle="tab">
                                <i class="fa fa-clock-o text-warning"></i> Chờ duyệt ({{ $stats['pending_count'] }})
                            </a>
                        </li>
                        <li role="presentation">
                            <a href="#history-tab" aria-controls="history-tab" role="tab" data-toggle="tab">
                                <i class="fa fa-history text-info"></i> Lịch sử duyệt (1 tháng)
                            </a>
                        </li>
                    </ul>
                    <!-- Chú thích trạng thái -->
                    <div class="panel panel-info" style="margin-bottom: 20px;">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <i class="fa fa-info-circle"></i> Chú thích trạng thái giải trình - HR
                            </h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="legend-item">
                                        <span class="legend-icon">🔵</span>
                                        <span class="label label-info">Chờ HR duyệt</span>
                                        <span class="legend-desc">Manager đã duyệt, chờ HR quyết định cuối cùng</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-icon">✅</span>
                                        <span class="label label-success">HR duyệt</span>
                                        <span class="legend-desc">HR đã phê duyệt - Hoàn tất quy trình</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="legend-item">
                                        <span class="legend-icon">❌</span>
                                        <span class="label label-danger">HR từ chối</span>
                                        <span class="legend-desc">HR đã từ chối giải trình</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-icon">💡</span>
                                        <span class="text-muted"><i class="fa fa-lightbulb-o"></i> Lưu ý:</span>
                                        <span class="legend-desc">Chỉ hiển thị đơn đã được Manager duyệt</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content">
                        <!-- Pending Tab -->
                        <div role="tabpanel" class="tab-pane active" id="pending-tab">
                            <!-- Select All Button - Always Visible -->
                            <div class="row" style="margin-bottom: 15px;">
                                <div class="col-md-12">
                                    <button type="button" class="btn btn-info btn-sm" id="selectAllBtn">
                                        <i class="fa fa-check-square"></i> Chọn tất cả
                                    </button>
                                    <small class="text-muted" style="margin-left: 10px;">
                                        <span id="selectedCount">0</span> giải trình đã chọn
                                    </small>
                                </div>
                            </div>

                            <!-- Bulk Actions Panel -->
                            <div id="bulkActionsPanel" class="bulk-actions-panel" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="bulk-info">
                                            <i class="fa fa-check-square-o"></i>
                                            <strong><span id="selectedCountInPanel">0</span> giải trình đã chọn</strong>
                                        </div>
                                    </div>
                                    <div class="col-md-6 text-right">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-success btn-sm" id="bulkApproveBtn">
                                                <i class="fa fa-check"></i> Duyệt tất cả
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" id="bulkRejectBtn">
                                                <i class="fa fa-times"></i> Từ chối tất cả
                                            </button>
                                            <button type="button" class="btn btn-default btn-sm" id="clearSelectionBtn">
                                                <i class="fa fa-refresh"></i> Bỏ chọn
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Filters for Pending -->
                            <div class="row" style="margin-bottom: 20px;">
                        <div class="col-md-4">
                            <label>Tìm theo tên nhân viên:</label>
                            <input type="text" id="employeeFilter" class="form-control" placeholder="Nhập tên nhân viên...">
                        </div>
                        <div class="col-md-3">
                            <label>Tháng:</label>
                            <select id="monthFilter" class="form-control">
                                <option value="">Tất cả tháng</option>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ $i == date('n') ? 'selected' : '' }}>
                                        Tháng {{ $i }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label>Năm:</label>
                            <select id="yearFilter" class="form-control">
                                @for($year = date('Y'); $year >= date('Y') - 2; $year--)
                                    <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label>&nbsp;</label>
                            <button type="button" id="applyFilter" class="btn btn-primary form-control">
                                <i class="fa fa-search"></i> Lọc
                            </button>
                        </div>
                    </div>

                    <!-- Results -->
                    <div id="explanationsList">
                    @if($groupedPending->count() > 0)
                        @foreach($groupedPending as $departmentKey => $explanations)
                            @php
                                [$businessDeptName, $staffDeptName] = explode('|', $departmentKey);
                                // Tạo ID an toàn cho jQuery selector
                                $departmentId = 'dept_' . md5($departmentKey);
                                $totalExplanations = $explanations->count();
                                $uniqueUsers = $explanations->groupBy('user_id')->count();
                            @endphp
                            <!-- Department Header - Modern Card Design -->
                            <div class="department-card" style="margin-bottom: 15px;">
                                <div class="department-header hr-header" data-toggle="collapse" data-target="#department-{{ $departmentId }}" aria-expanded="false">
                                    <div class="department-info">
                                        <div class="department-avatar">
                                            <div class="avatar-circle hr-avatar">
                                                <i class="fa fa-building"></i>
                                            </div>
                                        </div>
                                        <div class="department-details">
                                            <div class="department-name">
                                                <strong>{{ $businessDeptName }}</strong>
                                            </div>
                                            <div class="department-meta">
                                                <span class="department-staff">
                                                    <i class="fa fa-users"></i> {{ $staffDeptName }}
                                                </span>
                                                <span class="department-count">
                                                    <i class="fa fa-user"></i> {{ $uniqueUsers }} nhân viên
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="department-stats">
                                        <div class="hr-pending-count">
                                            <span class="count-number">{{ $totalExplanations }}</span>
                                            <span class="count-label">giải trình chờ duyệt</span>
                                        </div>
                                        <div class="expand-icon">
                                            <i class="fa fa-chevron-down"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Department Explanations -->
                            <div id="department-{{ $departmentId }}" class="panel-collapse collapse">
                                @foreach($explanations->groupBy('user_id') as $userId => $userExplanations)
                                    @php
                                        $user = $userExplanations->first()->user;
                                        $userCollapseId = 'user_' . $departmentId . '_' . $userId;
                                    @endphp

                                    <!-- User Card - Clickable -->
                                    <div class="user-card" style="margin: 10px 0;">
                                        <div class="user-header" data-toggle="collapse" data-target="#{{ $userCollapseId }}" aria-expanded="false">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6 style="margin: 0; font-weight: 600;">
                                                        <i class="fa fa-user text-info"></i>
                                                        <strong>{{ $user->name }}</strong>
                                                        <small class="text-muted">({{ $user->account }})</small>
                                                    </h6>
                                                </div>
                                                <div class="col-md-6 text-right">
                                                    <button type="button" class="btn btn-xs btn-info select-user-btn"
                                                            data-user-collapse-id="{{ $userCollapseId }}"
                                                            style="margin-right: 10px; padding: 2px 8px;">
                                                        <i class="fa fa-check-square-o"></i> Chọn tất cả
                                                    </button>
                                                    <small class="text-muted">{{ $userExplanations->count() }} giải trình</small>
                                                    <i class="fa fa-chevron-down" style="margin-left: 10px;"></i>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- User Explanations -->
                                        <div id="{{ $userCollapseId }}" class="panel-collapse collapse" style="margin-left: 20px;">
                                            @foreach($userExplanations as $explanation)
                                        <div class="panel panel-info" style="margin-bottom: 10px; margin-left: 20px;">
                                    <div class="panel-heading">
                                        <div class="row">
                                            <div class="col-md-1">
                                                <div class="checkbox-wrapper">
                                                    <input type="checkbox" class="explanation-checkbox"
                                                           data-explanation-id="{{ $explanation->id }}"
                                                           data-user-name="{{ $user->name }}"
                                                           id="hr_explanation_{{ $explanation->id }}">
                                                    <label for="hr_explanation_{{ $explanation->id }}" class="checkbox-label">
                                                        <i class="fa fa-square-o unchecked"></i>
                                                        <i class="fa fa-check-square checked"></i>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-7">
                                                <h5 class="panel-title">
                                                    <i class="fa fa-calendar"></i>
                                                    <strong>{{ $explanation->date->format('d/m/Y') }}</strong>
                                                    <small class="text-muted">({{ $explanation->date->locale('vi')->dayName }})</small>
                                                    @if($explanation->explanation_type === 'overtime')
                                                        <span class="label label-warning">OT</span>
                                                    @endif
                                                </h5>
                                                <small class="text-muted">
                                                    {{ $explanation->explanation_type_text }}
                                                    • Manager duyệt: {{ $explanation->manager_approved_at->format('d/m/Y H:i') }}
                                                </small>
                                            </div>
                                            <div class="col-md-4 text-right">
                                                <span class="label label-info">Chờ HR duyệt</span>
                                            </div>
                                        </div>
                                    </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6><strong>Nội dung giải trình:</strong></h6>
                                            <p class="well well-sm">{{ $explanation->explanation }}</p>

                                            @if($explanation->explanation_type === 'overtime' && $explanation->ot_hours)
                                                <h6><strong>Số giờ OT:</strong></h6>
                                                <p><span class="label label-info">{{ $explanation->ot_hours }} giờ</span></p>
                                            @endif

                                            @if($explanation->tagged_users && count($explanation->tagged_users) > 0)
                                                <h6><strong>Đã tag:</strong></h6>
                                                <p class="text-muted">{{ count($explanation->tagged_users) }} người được thông báo</p>
                                            @endif
                                        </div>
                                        <div class="col-md-4">
                                            <h6><strong>Quy trình duyệt:</strong></h6>
                                            <div class="list-group">
                                                <div class="list-group-item list-group-item-success">
                                                    <i class="fa fa-check text-success"></i>
                                                    <strong>Bước 1: Manager</strong>
                                                    <br><small class="text-success">
                                                        Đã duyệt bởi {{ $explanation->managerApprover->name ?? 'N/A' }}
                                                        <br>{{ $explanation->manager_approved_at->format('d/m/Y H:i') }}
                                                    </small>
                                                    @if($explanation->manager_note)
                                                        <br><small class="text-info">{{ $explanation->manager_note }}</small>
                                                    @endif
                                                </div>
                                                <div class="list-group-item list-group-item-info">
                                                    <i class="fa fa-building text-info"></i>
                                                    <strong>Bước 2: HCNS</strong>
                                                    <br><small class="text-info">Đang chờ bạn duyệt</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <h6><strong>Quyết định cuối cùng của HR:</strong></h6>
                                            <form class="hr-approval-form" data-explanation-id="{{ $explanation->id }}">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Quyết định:</label>
                                                            <select name="status" class="form-control" required>
                                                                <option value="">-- Chọn quyết định --</option>
                                                                <option value="approved">✓ Duyệt (Hoàn tất)</option>
                                                                <option value="rejected">✗ Từ chối</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Ghi chú (tùy chọn):</label>
                                                            <textarea name="note" class="form-control" rows="2" placeholder="Ghi chú của HR..."></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <button type="submit" class="btn btn-success">
                                                        <i class="fa fa-check"></i> Xác nhận quyết định cuối cùng
                                                    </button>
                                                    <button type="button" class="btn btn-default" onclick="window.location.reload()">
                                                        <i class="fa fa-refresh"></i> Làm mới
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                </div>
                                            @endforeach
                                        </div> <!-- End user explanations collapse -->
                                    </div> <!-- End user card -->
                                @endforeach
                            </div>
                        @endforeach
                    @else
                        <div class="alert alert-info text-center">
                            <i class="fa fa-info-circle fa-2x"></i>
                            <h4>Không có giải trình nào cần HR duyệt</h4>
                            <p>Hiện tại không có đơn giải trình chấm công nào cần HR duyệt.</p>
                            <p><small class="text-muted">Chỉ những đơn đã được manager duyệt mới hiển thị ở đây.</small></p>
                        </div>
                    @endif
                    </div> <!-- End explanationsList -->
                        </div> <!-- End pending-tab -->

                        <!-- History Tab -->
                        <div role="tabpanel" class="tab-pane" id="history-tab">
                            <!-- Filters for History -->
                            <div class="row" style="margin-bottom: 20px;">
                                <div class="col-md-4">
                                    <label>Tìm theo tên nhân viên:</label>
                                    <input type="text" id="historyEmployeeFilter" class="form-control" placeholder="Nhập tên nhân viên...">
                                </div>
                                <div class="col-md-3">
                                    <label>Tháng:</label>
                                    <select id="historyMonthFilter" class="form-control">
                                        <option value="">Tất cả tháng</option>
                                        @for($i = 1; $i <= 12; $i++)
                                            <option value="{{ $i }}" {{ $i == date('n') ? 'selected' : '' }}>
                                                Tháng {{ $i }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label>Năm:</label>
                                    <select id="historyYearFilter" class="form-control">
                                        @for($year = date('Y'); $year >= date('Y') - 2; $year--)
                                            <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>
                                                {{ $year }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label>&nbsp;</label>
                                    <button type="button" id="applyHistoryFilter" class="btn btn-primary form-control">
                                        <i class="fa fa-search"></i> Lọc
                                    </button>
                                </div>
                            </div>

                            <!-- History Results -->
                            <div id="historyList">
                            @if($groupedHistory->count() > 0)
                                @foreach($groupedHistory as $departmentKey => $explanations)
                                    @php
                                        [$businessDeptName, $staffDeptName] = explode('|', $departmentKey);
                                        $historyDeptId = 'history_dept_' . md5($departmentKey);
                                        $approvedCount = $explanations->where('hr_status', 'approved')->count();
                                        $rejectedCount = $explanations->where('hr_status', 'rejected')->count();
                                        $uniqueUsers = $explanations->groupBy('user_id')->count();
                                    @endphp
                                    <!-- Department Header - Modern Card Design -->
                                    <div class="department-card" style="margin-bottom: 15px;">
                                        <div class="department-header hr-history-header" data-toggle="collapse" data-target="#{{ $historyDeptId }}" aria-expanded="false">
                                            <div class="department-info">
                                                <div class="department-avatar">
                                                    <div class="avatar-circle hr-history-avatar">
                                                        <i class="fa fa-building"></i>
                                                    </div>
                                                </div>
                                                <div class="department-details">
                                                    <div class="department-name">
                                                        <strong>{{ $businessDeptName }}</strong>
                                                    </div>
                                                    <div class="department-meta">
                                                        <span class="department-staff">
                                                            <i class="fa fa-users"></i> {{ $staffDeptName }}
                                                        </span>
                                                        <span class="department-count">
                                                            <i class="fa fa-user"></i> {{ $uniqueUsers }} nhân viên
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="department-stats">
                                                <div class="hr-history-stats">
                                                    @if($approvedCount > 0)
                                                        <span class="stat-item approved">
                                                            <span class="stat-number">{{ $approvedCount }}</span>
                                                            <span class="stat-label">đã duyệt</span>
                                                        </span>
                                                    @endif
                                                    @if($rejectedCount > 0)
                                                        <span class="stat-item rejected">
                                                            <span class="stat-number">{{ $rejectedCount }}</span>
                                                            <span class="stat-label">từ chối</span>
                                                        </span>
                                                    @endif
                                                </div>
                                                <div class="expand-icon">
                                                    <i class="fa fa-chevron-down"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Department History -->
                                    <div id="{{ $historyDeptId }}" class="panel-collapse collapse">
                                        @foreach($explanations->groupBy('user_id') as $userId => $userExplanations)
                                            @php
                                                $user = $userExplanations->first()->user;
                                                $userHistoryId = 'history_user_' . $historyDeptId . '_' . $userId;
                                            @endphp

                                            <!-- User Card in History -->
                                            <div class="user-card" style="margin: 10px 0;">
                                                <div class="user-header" data-toggle="collapse" data-target="#{{ $userHistoryId }}" aria-expanded="false">
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <h6 style="margin: 0; font-weight: 600;">
                                                                <i class="fa fa-user text-info"></i>
                                                                <strong>{{ $user->name }}</strong>
                                                                <small class="text-muted">({{ $user->account }})</small>
                                                            </h6>
                                                        </div>
                                                        <div class="col-md-4 text-right">
                                                            @php
                                                                $userApproved = $userExplanations->where('hr_status', 'approved')->count();
                                                                $userRejected = $userExplanations->where('hr_status', 'rejected')->count();
                                                            @endphp
                                                            @if($userApproved > 0)
                                                                <span class="badge" style="background-color: #5cb85c;">{{ $userApproved }} duyệt</span>
                                                            @endif
                                                            @if($userRejected > 0)
                                                                <span class="badge" style="background-color: #d9534f;">{{ $userRejected }} từ chối</span>
                                                            @endif
                                                            <i class="fa fa-chevron-down" style="margin-left: 10px;"></i>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- User History Explanations -->
                                                <div id="{{ $userHistoryId }}" class="panel-collapse collapse" style="margin-left: 20px;">
                                                    @foreach($userExplanations as $explanation)
                                            <div class="panel {{ $explanation->hr_status === 'approved' ? 'panel-success' : 'panel-danger' }}" style="margin-bottom: 10px;">
                                                <div class="panel-heading">
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <h5 class="panel-title">
                                                                <i class="fa fa-calendar"></i>
                                                                <strong>{{ $explanation->date->format('d/m/Y') }}</strong>
                                                                <small class="text-muted">({{ $explanation->date->locale('vi')->dayName }})</small>
                                                                @if($explanation->explanation_type === 'overtime')
                                                                    <span class="label label-warning">OT</span>
                                                                @endif
                                                            </h5>
                                                            <small class="text-muted">
                                                                {{ $explanation->explanation_type_text }}
                                                                • HR duyệt: {{ $explanation->hr_approved_at->format('d/m/Y H:i') }}
                                                            </small>
                                                        </div>
                                                        <div class="col-md-4 text-right">
                                                            @if($explanation->hr_status === 'approved')
                                                                <span class="label label-success">✅ HR đã duyệt</span>
                                                            @else
                                                                <span class="label label-danger">❌ HR từ chối</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <h6><strong>Nội dung giải trình:</strong></h6>
                                                            <p class="well well-sm">{{ $explanation->explanation }}</p>

                                                            @if($explanation->explanation_type === 'overtime' && $explanation->ot_hours)
                                                                <h6><strong>Số giờ OT:</strong></h6>
                                                                <p><span class="label label-info">{{ $explanation->ot_hours }} giờ</span></p>
                                                            @endif
                                                        </div>
                                                        <div class="col-md-4">
                                                            <h6><strong>Quy trình duyệt:</strong></h6>
                                                            <div class="list-group">
                                                                <div class="list-group-item list-group-item-success">
                                                                    <i class="fa fa-check text-success"></i>
                                                                    <strong>Bước 1: Manager</strong>
                                                                    <br><small class="text-success">
                                                                        Đã duyệt bởi {{ $explanation->managerApprover->name ?? 'N/A' }}
                                                                        <br>{{ $explanation->manager_approved_at->format('d/m/Y H:i') }}
                                                                    </small>
                                                                    @if($explanation->manager_note)
                                                                        <br><small class="text-info">{{ $explanation->manager_note }}</small>
                                                                    @endif
                                                                </div>
                                                                <div class="list-group-item {{ $explanation->hr_status === 'approved' ? 'list-group-item-success' : 'list-group-item-danger' }}">
                                                                    <i class="fa {{ $explanation->hr_status === 'approved' ? 'fa-check text-success' : 'fa-times text-danger' }}"></i>
                                                                    <strong>Bước 2: HR</strong>
                                                                    <br><small class="{{ $explanation->hr_status === 'approved' ? 'text-success' : 'text-danger' }}">
                                                                        {{ $explanation->hr_status === 'approved' ? 'Đã duyệt' : 'Đã từ chối' }} bởi {{ $explanation->hrApprover->name ?? 'N/A' }}
                                                                        <br>{{ $explanation->hr_approved_at->format('d/m/Y H:i') }}
                                                                    </small>
                                                                    @if($explanation->hr_note)
                                                                        <br><small class="text-info">{{ $explanation->hr_note }}</small>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                                    @endforeach
                                                </div> <!-- End user history explanations -->
                                            </div> <!-- End user card -->
                                        @endforeach
                                    </div> <!-- End department history -->
                                @endforeach
                            @else
                                <div class="alert alert-info text-center">
                                    <i class="fa fa-info-circle fa-2x"></i>
                                    <h4>Chưa có lịch sử duyệt</h4>
                                    <p>Chưa có giải trình nào được HR duyệt trong 1 tháng gần đây.</p>
                                </div>
                            @endif
                            </div> <!-- End historyList -->
                        </div> <!-- End history-tab -->
                    </div> <!-- End tab-content -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('page.scripts')
<script>
$(document).ready(function() {
    // Handle collapse/expand icons for departments and users
    $('.panel-collapse').on('show.bs.collapse', function() {
        var $trigger = $(this).prev('.department-card, .employee-card, .user-card');
        $trigger.find('.fa-chevron-down').removeClass('fa-chevron-down').addClass('fa-chevron-up');
    });

    $('.panel-collapse').on('hide.bs.collapse', function() {
        var $trigger = $(this).prev('.department-card, .employee-card, .user-card');
        $trigger.find('.fa-chevron-up').removeClass('fa-chevron-up').addClass('fa-chevron-down');
    });

    // Function để hiển thị toast notification
    function showToast(type, title, message) {
        var toastClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

        var toastHtml = '<div class="alert ' + toastClass + ' alert-dismissible toast-notification" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">' +
            '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>' +
            '<h5><i class="fa ' + iconClass + '"></i> ' + title + '</h5>' +
            message +
        '</div>';

        $('body').append(toastHtml);

        // Tự động ẩn sau 5 giây
        setTimeout(function() {
            $('.toast-notification').fadeOut(500, function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Filter functionality for Pending tab
    $('#applyFilter, #employeeFilter').on('click keyup', function(e) {
        if (e.type === 'keyup' && e.keyCode !== 13) return; // Only trigger on Enter key

        var employeeName = $('#employeeFilter').val().toLowerCase();
        var month = $('#monthFilter').val();
        var year = $('#yearFilter').val();

        // Filter departments
        $('.department-card').each(function() {
            var $departmentCard = $(this);
            var $departmentCollapse = $departmentCard.next('.panel-collapse');
            var hasVisibleExplanations = false;

            // Check each user card in this department
            $departmentCollapse.find('.user-card').each(function() {
                var $userCard = $(this);
                var $userHeader = $userCard.find('.user-header');
                var $userCollapse = $userCard.find('.panel-collapse');
                var userText = $userHeader.find('h6').text().toLowerCase();
                var showUser = true;

                // Filter by employee name
                if (employeeName && !userText.includes(employeeName)) {
                    showUser = false;
                }

                if (showUser) {
                    // Check individual explanations for date filter
                    var hasVisibleUserExplanations = false;
                    $userCollapse.find('.panel-info').each(function() {
                        var $explanationPanel = $(this);
                        var dateText = $explanationPanel.find('.panel-heading h5').text();
                        var showExplanation = true;

                        // Filter by month/year
                        if (month || year) {
                            var dateMatch = dateText.match(/(\d{2})\/(\d{2})\/(\d{4})/);
                            if (dateMatch) {
                                var itemMonth = dateMatch[2];
                                var itemYear = dateMatch[3];

                                if (month && itemMonth != month.padStart(2, '0')) {
                                    showExplanation = false;
                                }
                                if (year && itemYear != year) {
                                    showExplanation = false;
                                }
                            }
                        }

                        if (showExplanation) {
                            $explanationPanel.show();
                            hasVisibleUserExplanations = true;
                            hasVisibleExplanations = true;
                        } else {
                            $explanationPanel.hide();
                        }
                    });

                    if (hasVisibleUserExplanations) {
                        $userCard.show();
                    } else {
                        $userCard.hide();
                    }
                } else {
                    $userCard.hide();
                }
            });

            // Show/hide department based on whether it has visible explanations
            if (hasVisibleExplanations) {
                $departmentCard.show();
                $departmentCollapse.show();
            } else {
                $departmentCard.hide();
                $departmentCollapse.hide();
            }
        });

        // Update bulk actions panel after filter
        updateBulkActionsPanel();
    });

    // Filter functionality for History tab - với AJAX load theo tháng
    $('#applyHistoryFilter').on('click', function() {
        var employeeName = $('#historyEmployeeFilter').val().toLowerCase();
        var month = $('#historyMonthFilter').val();
        var year = $('#historyYearFilter').val();

        // Nếu có filter theo tháng/năm, gọi API để load data mới
        if (month || year) {
            loadHrHistoryByMonth(month, year, employeeName);
        } else {
            // Nếu không có filter tháng/năm, chỉ filter theo tên
            filterHistoryByName(employeeName);
        }
    });

    // Filter theo tên nhân viên (Enter key)
    $('#historyEmployeeFilter').on('keyup', function(e) {
        if (e.keyCode !== 13) return; // Only trigger on Enter key
        $('#applyHistoryFilter').click();
    });

    // Function để load lịch sử HR theo tháng
    function loadHrHistoryByMonth(month, year, employeeName) {
        $.ajax({
            url: '{{ route("attendance-explanation.hr-history-by-month") }}',
            method: 'GET',
            data: {
                month: month,
                year: year
            },
            beforeSend: function() {
                $('#historyList').html('<div class="text-center"><i class="fa fa-spinner fa-spin fa-2x"></i><br>Đang tải...</div>');
            },
            success: function(response) {
                if (response.success) {
                    renderHrHistoryData(response.data, employeeName);
                } else {
                    $('#historyList').html('<div class="alert alert-danger">Có lỗi xảy ra khi tải dữ liệu</div>');
                }
            },
            error: function() {
                $('#historyList').html('<div class="alert alert-danger">Có lỗi xảy ra khi tải dữ liệu</div>');
            }
        });
    }

    // Function để render HR history data
    function renderHrHistoryData(groupedData, employeeNameFilter) {
        var html = '';

        if (Object.keys(groupedData).length === 0) {
            html = '<div class="alert alert-info text-center">' +
                   '<i class="fa fa-info-circle fa-2x"></i>' +
                   '<h4>Chưa có lịch sử duyệt</h4>' +
                   '<p>Không có dữ liệu cho tháng/năm đã chọn.</p>' +
                   '</div>';
        } else {
            Object.keys(groupedData).forEach(function(userId) {
                var explanations = groupedData[userId];
                var user = explanations[0].user;

                // Filter theo tên nếu có
                if (employeeNameFilter && !user.name.toLowerCase().includes(employeeNameFilter)) {
                    return;
                }

                var approvedCount = explanations.filter(e => e.hr_status === 'approved').length;
                var rejectedCount = explanations.filter(e => e.hr_status === 'rejected').length;

                html += '<div class="panel panel-default" style="margin-bottom: 5px;">';
                html += '<div class="panel-heading" style="cursor: pointer;" data-toggle="collapse" data-target="#history-employee-' + userId + '" aria-expanded="false">';
                html += '<div class="row">';
                html += '<div class="col-md-8">';
                html += '<h5 class="panel-title">';
                html += '<i class="fa fa-user"></i> <strong>' + user.name + '</strong>';
                html += '<small class="text-muted">(' + user.account + ')</small>';
                if (user.staff_department) {
                    html += '<small class="text-muted"> - ' + user.staff_department.name + '</small>';
                }
                html += '</h5>';
                html += '</div>';
                html += '<div class="col-md-4 text-right">';
                if (approvedCount > 0) {
                    html += '<span class="badge" style="background-color: #5cb85c;">' + approvedCount + ' đã duyệt</span> ';
                }
                if (rejectedCount > 0) {
                    html += '<span class="badge" style="background-color: #d9534f;">' + rejectedCount + ' từ chối</span> ';
                }
                html += '<i class="fa fa-chevron-down"></i>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '</div>';

                // Employee History
                html += '<div id="history-employee-' + userId + '" class="panel-collapse collapse">';
                explanations.forEach(function(explanation) {
                    var panelClass = explanation.hr_status === 'approved' ? 'panel-success' : 'panel-danger';
                    var statusIcon = explanation.hr_status === 'approved' ? '✅' : '❌';
                    var statusText = explanation.hr_status === 'approved' ? 'HR đã duyệt' : 'HR từ chối';

                    html += '<div class="panel ' + panelClass + '" style="margin-bottom: 10px;">';
                    html += '<div class="panel-heading">';
                    html += '<div class="row">';
                    html += '<div class="col-md-8">';
                    html += '<h5 class="panel-title">';
                    html += '<i class="fa fa-calendar"></i> <strong>' + formatDate(explanation.date) + '</strong>';
                    html += '<small class="text-muted">(' + getDayName(explanation.date) + ')</small>';
                    if (explanation.explanation_type === 'overtime') {
                        html += ' <span class="label label-warning">OT</span>';
                    }
                    html += '</h5>';
                    html += '<small class="text-muted">';
                    html += explanation.explanation_type_text + ' • HR duyệt: ' + formatDateTime(explanation.hr_approved_at);
                    html += '</small>';
                    html += '</div>';
                    html += '<div class="col-md-4 text-right">';
                    html += '<span class="label ' + (explanation.hr_status === 'approved' ? 'label-success' : 'label-danger') + '">' + statusIcon + ' ' + statusText + '</span>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';

                    // Panel body với thông tin chi tiết
                    html += '<div class="panel-body">';
                    html += '<div class="row">';
                    html += '<div class="col-md-8">';
                    html += '<h6><strong>Nội dung giải trình:</strong></h6>';
                    html += '<p class="well well-sm">' + explanation.explanation + '</p>';
                    if (explanation.explanation_type === 'overtime' && explanation.ot_hours) {
                        html += '<h6><strong>Số giờ OT:</strong></h6>';
                        html += '<p><span class="label label-info">' + explanation.ot_hours + ' giờ</span></p>';
                    }
                    html += '</div>';
                    html += '<div class="col-md-4">';
                    html += '<h6><strong>Quy trình duyệt:</strong></h6>';
                    html += '<div class="list-group">';

                    // Manager approval
                    html += '<div class="list-group-item list-group-item-success">';
                    html += '<i class="fa fa-check text-success"></i>';
                    html += '<strong>Bước 1: Manager</strong>';
                    html += '<br><small class="text-success">';
                    html += 'Đã duyệt bởi ' + (explanation.manager_approver ? explanation.manager_approver.name : 'N/A');
                    html += '<br>' + formatDateTime(explanation.manager_approved_at);
                    html += '</small>';
                    if (explanation.manager_note) {
                        html += '<br><small class="text-info">' + explanation.manager_note + '</small>';
                    }
                    html += '</div>';

                    // HR approval
                    var hrClass = explanation.hr_status === 'approved' ? 'list-group-item-success' : 'list-group-item-danger';
                    var hrIcon = explanation.hr_status === 'approved' ? 'fa-check text-success' : 'fa-times text-danger';

                    html += '<div class="list-group-item ' + hrClass + '">';
                    html += '<i class="fa ' + hrIcon + '"></i>';
                    html += '<strong>Bước 2: HR</strong>';
                    html += '<br><small class="' + (explanation.hr_status === 'approved' ? 'text-success' : 'text-danger') + '">';
                    html += statusText + ' bởi ' + (explanation.hr_approver ? explanation.hr_approver.name : 'N/A');
                    html += '<br>' + formatDateTime(explanation.hr_approved_at);
                    html += '</small>';
                    if (explanation.hr_note) {
                        html += '<br><small class="text-info">' + explanation.hr_note + '</small>';
                    }
                    html += '</div>';

                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                });
                html += '</div>';
            });
        }

        $('#historyList').html(html);
    }

    // Function để filter chỉ theo tên (không load lại data)
    function filterHistoryByName(employeeName) {
        $('#history-tab .panel-default').each(function() {
            var $employeePanel = $(this);
            var employeeText = $employeePanel.find('.panel-title').text().toLowerCase();

            if (!employeeName || employeeText.includes(employeeName)) {
                $employeePanel.show();
                $employeePanel.next('.panel-collapse').show();
            } else {
                $employeePanel.hide();
                $employeePanel.next('.panel-collapse').hide();
            }
        });
    }

    // Helper functions
    function formatDate(dateString) {
        var date = new Date(dateString);
        return date.getDate().toString().padStart(2, '0') + '/' +
               (date.getMonth() + 1).toString().padStart(2, '0') + '/' +
               date.getFullYear();
    }

    function formatDateTime(dateTimeString) {
        var date = new Date(dateTimeString);
        return date.getDate().toString().padStart(2, '0') + '/' +
               (date.getMonth() + 1).toString().padStart(2, '0') + '/' +
               date.getFullYear() + ' ' +
               date.getHours().toString().padStart(2, '0') + ':' +
               date.getMinutes().toString().padStart(2, '0');
    }

    function getDayName(dateString) {
        var date = new Date(dateString);
        var days = ['Chủ nhật', 'Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Thứ bảy'];
        return days[date.getDay()];
    }

    // Xử lý form duyệt HR
    $('.hr-approval-form').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var explanationId = form.data('explanation-id');
        var status = form.find('select[name="status"]').val();
        var note = form.find('textarea[name="note"]').val();

        if (!status) {
            alert('Vui lòng chọn quyết định!');
            return;
        }

        var confirmMessage = status === 'approved' ?
            'Bạn có chắc chắn muốn DUYỆT giải trình này?\n\nĐây là bước cuối cùng, sau khi duyệt đơn sẽ HOÀN TẤT!' :
            'Bạn có chắc chắn muốn TỪ CHỐI giải trình này?';

        if (!confirm(confirmMessage)) {
            return;
        }

        // Disable form
        form.find('button').prop('disabled', true);

        $.ajax({
            url: '/attendance-explanation/' + explanationId + '/hr-approve',
            method: 'POST',
            data: {
                status: status,
                note: note,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    showToast('success', 'Thành công!', response.message);
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('error', 'Lỗi!', response.message);
                    form.find('button').prop('disabled', false);
                }
            },
            error: function(xhr) {
                var message = 'Có lỗi xảy ra!';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showToast('error', 'Lỗi!', message);
                form.find('button').prop('disabled', false);
            }
        });
    });

    // Bulk Actions Functionality for HR
    var selectedExplanations = [];

    // Checkbox change event
    $(document).on('change', '.explanation-checkbox', function() {
        var explanationId = $(this).data('explanation-id');
        var userName = $(this).data('user-name');
        var $panel = $(this).closest('.panel');

        if ($(this).is(':checked')) {
            // Add to selection
            selectedExplanations.push({
                id: explanationId,
                userName: userName,
                element: $panel
            });
            $panel.addClass('explanation-selected');
        } else {
            // Remove from selection
            selectedExplanations = selectedExplanations.filter(function(item) {
                return item.id !== explanationId;
            });
            $panel.removeClass('explanation-selected');
        }

        updateBulkActionsPanel();
    });

    // Update bulk actions panel
    function updateBulkActionsPanel() {
        var count = selectedExplanations.length;
        var $allCheckboxes = $('#pending-tab .explanation-checkbox');
        var totalCheckboxes = $allCheckboxes.length;

        // Update both counters
        $('#selectedCount').text(count);
        $('#selectedCountInPanel').text(count);

        if (count > 0) {
            $('#bulkActionsPanel').show();
        } else {
            $('#bulkActionsPanel').hide();
        }

        // Update select all button text
        if (count === totalCheckboxes && totalCheckboxes > 0) {
            $('#selectAllBtn').html('<i class="fa fa-minus-square"></i> Bỏ chọn tất cả');
        } else {
            $('#selectAllBtn').html('<i class="fa fa-check-square"></i> Chọn tất cả');
        }

        // Update user select buttons
        updateUserSelectButtons();
    }

    // Function để update text của nút "Chọn tất cả" user
    function updateUserSelectButtons() {
        $('.select-user-btn').each(function() {
            var userCollapseId = $(this).data('user-collapse-id');
            var $userCollapse = $('#' + userCollapseId);
            var $userCheckboxes = $userCollapse.find('.explanation-checkbox');
            var checkedCount = $userCheckboxes.filter(':checked').length;
            var totalCount = $userCheckboxes.length;

            if (checkedCount === totalCount && totalCount > 0) {
                $(this).html('<i class="fa fa-square"></i> Bỏ chọn tất cả');
            } else {
                $(this).html('<i class="fa fa-check-square-o"></i> Chọn tất cả');
            }
        });
    }

    // Bulk approve button
    $('#bulkApproveBtn').on('click', function() {
        if (selectedExplanations.length === 0) {
            alert('Vui lòng chọn ít nhất một giải trình!');
            return;
        }
        showBulkActionModal('approved', 'DUYỆT');
    });

    // Bulk reject button
    $('#bulkRejectBtn').on('click', function() {
        if (selectedExplanations.length === 0) {
            alert('Vui lòng chọn ít nhất một giải trình!');
            return;
        }
        showBulkActionModal('rejected', 'TỪ CHỐI');
    });

    // Select all button (global)
    $('#selectAllBtn').on('click', function() {
        // Lấy tất cả checkbox trong tab pending, không chỉ visible
        var $allCheckboxes = $('#pending-tab .explanation-checkbox');

        // Kiểm tra nếu không có checkbox nào
        if ($allCheckboxes.length === 0) {
            alert('Không có giải trình nào để chọn!');
            return;
        }

        var allChecked = $allCheckboxes.filter(':checked').length === $allCheckboxes.length;

        if (allChecked) {
            // Nếu tất cả đã được chọn, bỏ chọn tất cả
            $allCheckboxes.prop('checked', false).trigger('change');
        } else {
            // Chọn tất cả - mở tất cả panels (department và user) trước khi chọn
            $('#pending-tab .panel-collapse').collapse('show');
            setTimeout(function() {
                $allCheckboxes.prop('checked', true).trigger('change');
            }, 500); // Đợi animation collapse hoàn thành cho cả 2 cấp
        }
    });

    // Select all for specific user
    $(document).on('click', '.select-user-btn', function(e) {
        e.stopPropagation(); // Ngăn không cho trigger collapse

        var userCollapseId = $(this).data('user-collapse-id');
        var $userCollapse = $('#' + userCollapseId);
        var $userCheckboxes = $userCollapse.find('.explanation-checkbox');

        if ($userCheckboxes.length === 0) {
            alert('Không có giải trình nào của nhân viên này!');
            return;
        }

        var allUserChecked = $userCheckboxes.filter(':checked').length === $userCheckboxes.length;

        if (allUserChecked) {
            // Bỏ chọn tất cả của user này
            $userCheckboxes.prop('checked', false).trigger('change');
            $(this).html('<i class="fa fa-check-square-o"></i> Chọn tất cả');
        } else {
            // Chọn tất cả của user này - mở user panel trước
            $userCollapse.collapse('show');
            setTimeout(function() {
                $userCheckboxes.prop('checked', true).trigger('change');
            }, 300);
            $(this).html('<i class="fa fa-square"></i> Bỏ chọn tất cả');
        }
    });

    // Clear selection button
    $('#clearSelectionBtn').on('click', function() {
        $('.explanation-checkbox').prop('checked', false);
        $('.panel').removeClass('explanation-selected');
        selectedExplanations = [];
        updateBulkActionsPanel();
        $('#selectAllBtn').html('<i class="fa fa-check-square"></i> Chọn tất cả');
    });

    // Show bulk action modal
    function showBulkActionModal(action, actionText) {
        $('#bulkActionType').text(actionText);
        $('#bulkActionCount').text(selectedExplanations.length);

        // Populate list
        var listHtml = '';
        selectedExplanations.forEach(function(item) {
            var dateText = item.element.find('.panel-title strong').text();
            var typeText = item.element.find('.panel-title small').text();

            listHtml += '<div class="list-group-item">';
            listHtml += '<strong>' + item.userName + '</strong>';
            listHtml += '<br><small>' + dateText + ' - ' + typeText + '</small>';
            listHtml += '</div>';
        });
        $('#bulkActionList').html(listHtml);

        // Store action for confirmation
        $('#confirmBulkAction').data('action', action);

        $('#bulkActionModal').modal('show');
    }

    // Confirm bulk action
    $('#confirmBulkAction').on('click', function() {
        var action = $(this).data('action');
        var note = $('#bulkNote').val();
        var explanationIds = selectedExplanations.map(function(item) {
            return item.id;
        });

        if (explanationIds.length === 0) {
            alert('Không có giải trình nào được chọn!');
            return;
        }

        var confirmMessage = action === 'approved' ?
            'Bạn có chắc chắn muốn DUYỆT ' + explanationIds.length + ' giải trình đã chọn?\n\nĐây là bước cuối cùng, sau khi duyệt các đơn sẽ HOÀN TẤT!' :
            'Bạn có chắc chắn muốn TỪ CHỐI ' + explanationIds.length + ' giải trình đã chọn?';

        if (!confirm(confirmMessage)) {
            return;
        }

        // Disable button
        $(this).prop('disabled', true);

        $.ajax({
            url: '{{ route("attendance-explanation.hr-bulk-approve") }}',
            method: 'POST',
            data: {
                explanation_ids: explanationIds,
                status: action,
                note: note,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    showToast('success', 'Thành công!', response.message);
                    $('#bulkActionModal').modal('hide');
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showToast('error', 'Lỗi!', response.message);
                    $('#confirmBulkAction').prop('disabled', false);
                }
            },
            error: function(xhr) {
                var message = 'Có lỗi xảy ra!';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showToast('error', 'Lỗi!', message);
                $('#confirmBulkAction').prop('disabled', false);
            }
        });
    });

    // Reset modal when hidden
    $('#bulkActionModal').on('hidden.bs.modal', function() {
        $('#bulkNote').val('');
        $('#confirmBulkAction').prop('disabled', false);
    });
});
</script>

<style>
/* Legend styles */
.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 5px 0;
}

.legend-icon {
    font-size: 16px;
    margin-right: 8px;
    width: 20px;
    text-align: center;
}

.legend-item .label {
    margin-right: 10px;
    min-width: 120px;
    text-align: center;
}

.legend-desc {
    color: #666;
    font-size: 13px;
    font-style: italic;
}
</style>
<style>
/* HR Employee Card Styles */
.employee-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;
    overflow: hidden;
}

.employee-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.employee-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    cursor: pointer;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #e8e8e8;
}

.hr-header:hover {
    background: linear-gradient(135deg, #e8f5e8 0%, #f8f9fa 100%);
}

.hr-history-header:hover {
    background: linear-gradient(135deg, #fff3cd 0%, #f8f9fa 100%);
}

.employee-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.employee-avatar {
    margin-right: 16px;
}

.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    text-transform: uppercase;
}

.hr-avatar {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3) !important;
}

.hr-history-avatar {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3) !important;
}

.employee-details {
    flex: 1;
}

.employee-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.employee-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    color: #7f8c8d;
    font-size: 14px;
}

.employee-account,
.employee-department {
    display: flex;
    align-items: center;
    gap: 6px;
}

.employee-account i,
.employee-department i {
    color: #95a5a6;
}

.employee-stats {
    display: flex;
    align-items: center;
    gap: 16px;
}

.hr-pending-count {
    text-align: center;
    padding: 8px 16px;
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.count-number {
    display: block;
    font-size: 20px;
    font-weight: bold;
    line-height: 1;
}

.count-label {
    display: block;
    font-size: 12px;
    opacity: 0.9;
}

.hr-history-stats {
    display: flex;
    gap: 12px;
}

.stat-item {
    text-align: center;
    padding: 6px 12px;
    border-radius: 16px;
    color: white;
    font-size: 12px;
    font-weight: 500;
}

.stat-item.approved {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.stat-item.rejected {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
}

.stat-number {
    display: block;
    font-size: 16px;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 10px;
    opacity: 0.9;
}

.expand-icon {
    color: #95a5a6;
    font-size: 16px;
    transition: transform 0.3s ease;
}

.employee-header[aria-expanded="true"] .expand-icon {
    transform: rotate(180deg);
}

/* Bulk Actions Styles */
.bulk-actions-panel {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.bulk-info {
    font-size: 16px;
    font-weight: 600;
    color: #155724;
}

.bulk-info i {
    color: #28a745;
    margin-right: 8px;
}

.checkbox-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.explanation-checkbox {
    display: none;
}

.checkbox-label {
    cursor: pointer;
    font-size: 18px;
    color: #95a5a6;
    transition: all 0.3s ease;
    margin: 0;
}

.checkbox-label .checked {
    display: none;
    color: #27ae60;
}

.checkbox-label .unchecked {
    display: inline;
}

.explanation-checkbox:checked + .checkbox-label .checked {
    display: inline;
}

.explanation-checkbox:checked + .checkbox-label .unchecked {
    display: none;
}

.checkbox-label:hover {
    color: #3498db;
    transform: scale(1.1);
}

.explanation-checkbox:checked + .checkbox-label:hover {
    color: #27ae60;
}

/* Selected explanation highlight */
.explanation-selected {
    background-color: #d4edda !important;
    border-left: 4px solid #28a745 !important;
}

.explanation-selected .panel-heading {
    background-color: #c3e6cb !important;
}

/* Responsive */
@media (max-width: 768px) {
    .employee-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
    }

    .employee-info {
        width: 100%;
    }

    .employee-stats {
        width: 100%;
        justify-content: space-between;
    }

    .employee-meta {
        flex-direction: column;
        gap: 8px;
    }

    .avatar-circle {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    .employee-name {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .employee-header {
        padding: 12px;
    }

    .hr-history-stats {
        flex-direction: column;
        gap: 8px;
    }

    .stat-item {
        padding: 4px 8px;
    }

    .hr-pending-count {
        padding: 6px 12px;
    }

    .count-number {
        font-size: 18px;
    }
}
</style>

<!-- Modal xác nhận bulk actions -->
<div class="modal fade" id="bulkActionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">
                    <i class="fa fa-check-square-o"></i> Xác nhận duyệt hàng loạt - HR
                </h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    Bạn đang thực hiện <strong id="bulkActionType"></strong> cho <strong id="bulkActionCount"></strong> giải trình đã chọn.
                </div>

                <div id="bulkActionList" class="list-group" style="max-height: 300px; overflow-y: auto;">
                    <!-- Danh sách giải trình sẽ được load bằng JavaScript -->
                </div>

                <div class="form-group">
                    <label for="bulkNote">Ghi chú chung (tùy chọn):</label>
                    <textarea id="bulkNote" class="form-control" rows="3" maxlength="1000" placeholder="Ghi chú áp dụng cho tất cả giải trình được chọn..."></textarea>
                    <small class="text-muted">Tối đa 1000 ký tự</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <i class="fa fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-primary" id="confirmBulkAction">
                    <i class="fa fa-check"></i> Xác nhận thực hiện
                </button>
            </div>
        </div>
    </div>
</div>
@endsection
