@extends('layouts.authenticated')

@section('page.title', 'Xác nhận giải trình chấm công')

@section('page.breadcrumb')
    <ol class="breadcrumb">
        <li><a href="{{ route('auth.home') }}"><i class="fa fa-dashboard"></i> Trang chủ</a></li>
        <li><a href="{{ route('attendance-explanation.index') }}">Giải trình chấm công</a></li>
        <li class="active">Xác nhận giải trình</li>
    </ol>
@endsection

@section('page.content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-user-check"></i> Xác nhận giải trình chấm công
                        <span class="badge badge-warning pull-right" id="pending-count">0 đơn chờ xác nhận</span>
                    </h3>
                </div>
                <div class="panel-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" role="tablist" style="margin-bottom: 20px;">
                        <li role="presentation" class="active">
                            <a href="#pending-tab" aria-controls="pending-tab" role="tab" data-toggle="tab">
                                <i class="fa fa-clock-o text-warning"></i> Chờ xác nhận (<span id="pending-tab-count">0</span>)
                            </a>
                        </li>
                        <li role="presentation">
                            <a href="#history-tab" aria-controls="history-tab" role="tab" data-toggle="tab">
                                <i class="fa fa-history text-info"></i> Lịch sử xác nhận
                            </a>
                        </li>
                    </ul>

                    <!-- Chú thích trạng thái -->
                    <div class="panel panel-info" style="margin-bottom: 20px;">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <i class="fa fa-info-circle"></i> Chú thích trạng thái xác nhận
                            </h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="legend-item">
                                        <span class="legend-icon">🟡</span>
                                        <span class="label label-warning">Chờ xác nhận</span>
                                        <span class="legend-desc">Đang chờ bạn xác nhận</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-icon">🟢</span>
                                        <span class="label label-success">Đã xác nhận</span>
                                        <span class="legend-desc">Bạn đã xác nhận đồng ý</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="legend-item">
                                        <span class="legend-icon">🔴</span>
                                        <span class="label label-danger">Đã từ chối</span>
                                        <span class="legend-desc">Bạn đã từ chối giải trình</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content">
                        <!-- Pending Tab -->
                        <div role="tabpanel" class="tab-pane active" id="pending-tab">
                            <!-- Filters for Pending -->
                            <div class="row" style="margin-bottom: 20px;">
                                <div class="col-md-4">
                                    <label>Tìm theo tên người tạo:</label>
                                    <input type="text" id="employeeFilter" class="form-control" placeholder="Nhập tên người tạo giải trình...">
                                </div>
                                <div class="col-md-3">
                                    <label>Tháng:</label>
                                    <select id="monthFilter" class="form-control">
                                        <option value="">Tất cả tháng</option>
                                        @for($i = 1; $i <= 12; $i++)
                                            <option value="{{ $i }}" {{ $i == date('n') ? 'selected' : '' }}>
                                                Tháng {{ $i }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label>Năm:</label>
                                    <select id="yearFilter" class="form-control">
                                        @for($year = date('Y'); $year >= date('Y') - 2; $year--)
                                            <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>
                                                {{ $year }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label>&nbsp;</label>
                                    <button type="button" id="applyFilter" class="btn btn-primary form-control">
                                        <i class="fa fa-search"></i> Lọc
                                    </button>
                                </div>
                            </div>

                            <!-- Bulk Actions Panel -->
                            <div id="bulkActionsPanel" class="bulk-actions-panel" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="bulk-info">
                                            <i class="fa fa-check-square-o"></i>
                                            <span id="selectedCount">0</span> giải trình đã chọn
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-info btn-sm btn-block" id="selectAllBtn">
                                            <i class="fa fa-check-square"></i> Chọn tất cả
                                        </button>
                                    </div>
                                    <div class="col-md-4 text-right">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-success btn-sm" id="bulkConfirmBtn">
                                                <i class="fa fa-check"></i> Xác nhận tất cả
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" id="bulkRejectBtn">
                                                <i class="fa fa-times"></i> Từ chối tất cả
                                            </button>
                                            <button type="button" class="btn btn-default btn-sm" id="clearSelectionBtn">
                                                <i class="fa fa-refresh"></i> Bỏ chọn
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Results -->
                            <div id="explanationsList">
                                <!-- Danh sách sẽ được load bằng JavaScript -->
                            </div>
                        </div>

                        <!-- History Tab -->
                        <div role="tabpanel" class="tab-pane" id="history-tab">
                            <!-- Filters for History -->
                            <div class="row" style="margin-bottom: 20px;">
                                <div class="col-md-4">
                                    <label>Tìm theo tên người tạo:</label>
                                    <input type="text" id="historyEmployeeFilter" class="form-control" placeholder="Nhập tên người tạo...">
                                </div>
                                <div class="col-md-3">
                                    <label>Trạng thái:</label>
                                    <select id="historyStatusFilter" class="form-control">
                                        <option value="">Tất cả</option>
                                        <option value="confirmed">Đã xác nhận</option>
                                        <option value="rejected">Đã từ chối</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label>Tháng xác nhận:</label>
                                    <select id="historyMonthFilter" class="form-control">
                                        <option value="">Tất cả tháng</option>
                                        @for($i = 1; $i <= 12; $i++)
                                            <option value="{{ $i }}" {{ $i == date('n') ? 'selected' : '' }}>
                                                Tháng {{ $i }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label>&nbsp;</label>
                                    <div class="btn-group" style="width: 100%;">
                                        <button type="button" id="applyHistoryFilter" class="btn btn-info" style="width: 60%;">
                                            <i class="fa fa-search"></i> Lọc
                                        </button>
                                        <button type="button" id="resetHistoryFilter" class="btn btn-default" style="width: 40%;">
                                            <i class="fa fa-refresh"></i> Reset
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- History Results -->
                            <div id="historyList">
                                <!-- Lịch sử sẽ được load bằng JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal xác nhận giải trình -->
<div class="modal fade" id="confirmExplanationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" style="color: white;">&times;</button>
                <h4 class="modal-title" style="color: white;">
                    <i class="fa fa-user-check"></i> Xác nhận giải trình chấm công
                </h4>
            </div>
            <form id="confirmExplanationForm" method="POST">
                @csrf
                <input type="hidden" id="confirm_explanation_id" name="explanation_id">
                <div class="modal-body">
                    <!-- Thông tin giải trình -->
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <i class="fa fa-info-circle"></i> Thông tin giải trình
                            </h5>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong><i class="fa fa-user"></i> Người tạo:</strong> <span id="confirm_creator_name" class="text-primary"></span></p>
                                    <p><strong><i class="fa fa-calendar"></i> Ngày:</strong> <span id="confirm_date" class="text-info"></span></p>
                                    <p><strong><i class="fa fa-tag"></i> Loại:</strong> <span id="confirm_type" class="text-warning"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <div id="confirm_ot_hours_info" style="display: none;">
                                        <p><strong><i class="fa fa-clock-o"></i> Số giờ OT:</strong> <span id="confirm_ot_hours" class="text-success"></span> giờ</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <p><strong><i class="fa fa-comment"></i> Lý do:</strong></p>
                                    <div class="well well-sm" id="confirm_explanation" style="background-color: #f9f9f9; border-left: 4px solid #3c8dbc;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quyết định xác nhận -->
                    <div class="panel panel-warning">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <i class="fa fa-check-circle"></i> Quyết định của bạn
                            </h5>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="confirm_status">Quyết định:</label>
                                        <select class="form-control" id="confirm_status" name="status" required>
                                            <option value="">-- Chọn quyết định --</option>
                                            <option value="confirmed">✓ Xác nhận đồng ý</option>
                                            <option value="rejected">✗ Từ chối</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="confirm_note">Ghi chú (tùy chọn):</label>
                                        <textarea class="form-control" id="confirm_note" name="note" rows="2" maxlength="1000" placeholder="Ghi chú của bạn về quyết định này..."></textarea>
                                        <small class="text-muted">Tối đa 1000 ký tự</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        <i class="fa fa-times"></i> Hủy
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-check"></i> Xác nhận quyết định
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal xác nhận bulk actions -->
<div class="modal fade" id="bulkActionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">
                    <i class="fa fa-check-square-o"></i> Xác nhận hàng loạt - Tagged User
                </h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    Bạn đang thực hiện <strong id="bulkActionType"></strong> cho <strong id="bulkActionCount"></strong> giải trình đã chọn.
                </div>

                <div id="bulkActionList" class="list-group" style="max-height: 300px; overflow-y: auto;">
                    <!-- Danh sách giải trình sẽ được load bằng JavaScript -->
                </div>

                <div class="form-group">
                    <label for="bulkNote">Ghi chú chung (tùy chọn):</label>
                    <textarea id="bulkNote" class="form-control" rows="3" maxlength="1000" placeholder="Ghi chú áp dụng cho tất cả giải trình được chọn..."></textarea>
                    <small class="text-muted">Tối đa 1000 ký tự</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">
                    <i class="fa fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-primary" id="confirmBulkAction">
                    <i class="fa fa-check"></i> Xác nhận thực hiện
                </button>
            </div>
        </div>
    </div>
</div>
@endsection



@section('page.scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script>
$(document).ready(function() {
    // Load danh sách khi trang load
    loadTaggedExplanations();

    // Function để hiển thị toast notification
    function showToast(type, title, message) {
        var toastClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';

        var toastHtml = '<div class="alert ' + toastClass + ' alert-dismissible" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">' +
            '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>' +
            '<h5><i class="fa ' + iconClass + '"></i> ' + title + '</h5>' +
            message +
        '</div>';

        $('body').append(toastHtml);

        // Tự động ẩn sau 5 giây
        setTimeout(function() {
            $('.alert').fadeOut(500, function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Filter functionality for pending tab
    $('#applyFilter, #employeeFilter').on('click keyup', function(e) {
        if (e.type === 'keyup' && e.keyCode !== 13) return; // Only trigger on Enter key
        loadTaggedExplanations();
    });

    // History tab filters
    $('#applyHistoryFilter').on('click', function() {
        loadTaggedHistory();
    });

    $('#resetHistoryFilter').on('click', function() {
        $('#historyEmployeeFilter').val('');
        $('#historyStatusFilter').val('');
        $('#historyMonthFilter').val('');
        loadTaggedHistory();
    });

    // Tab change events
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        var target = $(e.target).attr("href");
        if (target === '#history-tab') {
            loadTaggedHistory();
        } else if (target === '#pending-tab') {
            loadTaggedExplanations();
        }
    });

    // Xử lý submit form xác nhận
    $(document).on('submit', '.tagged-confirmation-form', function(e) {
        e.preventDefault();

        var $form = $(this);
        var explanationId = $form.data('explanation-id');
        var formData = $form.serialize();

        $.ajax({
            url: '/attendance-explanation/' + explanationId + '/tagged-user-confirm',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showToast('success', 'Thành công!', response.message);
                    loadTaggedExplanations(); // Reload danh sách
                    loadTaggedHistory(); // Reload lịch sử
                } else {
                    showToast('error', 'Lỗi!', response.message || 'Có lỗi xảy ra');
                }
            },
            error: function(xhr) {
                var message = 'Có lỗi xảy ra khi xử lý yêu cầu';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    var errors = xhr.responseJSON.errors;
                    message = Object.values(errors).flat().join('<br>');
                }
                showToast('error', 'Lỗi!', message);
            }
        });
    });
});

function loadTaggedExplanations() {
    var employeeName = $('#employeeFilter').val();
    var month = $('#monthFilter').val();
    var year = $('#yearFilter').val();

    $.get('/attendance-explanation/tagged-explanations', {
        employee_name: employeeName,
        month: month,
        year: year
    })
    .done(function(response) {
        if (response.success) {
            renderTaggedExplanations(response.data);
        } else {
            $('#explanationsList').html('<div class="alert alert-warning">Không thể tải danh sách giải trình</div>');
        }
    })
    .fail(function() {
        $('#explanationsList').html('<div class="alert alert-danger">Có lỗi xảy ra khi tải danh sách</div>');
    });
}

function loadTaggedHistory() {
    var employeeName = $('#historyEmployeeFilter').val();
    var status = $('#historyStatusFilter').val();
    var month = $('#historyMonthFilter').val();

    $.get('/attendance-explanation/tagged-history', {
        employee_name: employeeName,
        status: status,
        month: month
    })
    .done(function(response) {
        if (response.success) {
            renderTaggedHistory(response.data);
        } else {
            $('#historyList').html('<div class="alert alert-warning">Không thể tải lịch sử xác nhận</div>');
        }
    })
    .fail(function() {
        $('#historyList').html('<div class="alert alert-danger">Có lỗi xảy ra khi tải lịch sử</div>');
    });
}

function renderTaggedExplanations(explanations) {
    var html = '';
    var pendingCount = 0;

    if (explanations.length === 0) {
        html = '<div class="alert alert-info text-center">';
        html += '<i class="fa fa-info-circle fa-2x"></i>';
        html += '<h4>Không có giải trình nào cần xác nhận</h4>';
        html += '<p>Hiện tại không có đơn giải trình chấm công nào cần bạn xác nhận.</p>';
        html += '</div>';
    } else {
        // Group by creator (user who created the explanation)
        var groupedByCreator = {};
        explanations.forEach(function(explanation) {
            var creatorId = explanation.created_by;
            var creatorName = explanation.creator_name || 'Unknown';
            if (!groupedByCreator[creatorId]) {
                groupedByCreator[creatorId] = {
                    name: creatorName,
                    explanations: []
                };
            }
            groupedByCreator[creatorId].explanations.push(explanation);

            if (explanation.tagged_user_status === 'pending') {
                pendingCount++;
            }
        });

        Object.keys(groupedByCreator).forEach(function(creatorId) {
            var creator = groupedByCreator[creatorId];
            var creatorExplanations = creator.explanations;
            var creatorPendingCount = creatorExplanations.filter(e => e.tagged_user_status === 'pending').length;

            // Employee Header - Modern Card Design
            html += '<div class="employee-card" style="margin-bottom: 15px;">';
            html += '<div class="employee-header tagged-header" data-toggle="collapse" data-target="#creator-' + creatorId + '" aria-expanded="false">';
            html += '<div class="employee-info">';
            html += '<div class="employee-avatar">';
            html += '<div class="avatar-circle tagged-avatar">';
            html += creator.name.substring(0, 2).toUpperCase();
            html += '</div>';
            html += '</div>';
            html += '<div class="employee-details">';
            html += '<div class="employee-name">' + creator.name + '</div>';
            html += '<div class="employee-meta">';
            html += '<span class="employee-account"><i class="fa fa-user-circle"></i> Người tạo giải trình</span>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '<div class="employee-stats">';
            if (creatorPendingCount > 0) {
                html += '<div class="tagged-pending-count">';
                html += '<span class="count-number">' + creatorPendingCount + '</span>';
                html += '<span class="count-label">chờ xác nhận</span>';
                html += '</div>';
            }
            html += '<div class="expand-icon">';
            html += '<i class="fa fa-chevron-down"></i>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';

            // Employee Explanations
            html += '<div id="creator-' + creatorId + '" class="panel-collapse collapse">';
            creatorExplanations.forEach(function(explanation) {
                var statusClass = getStatusClass(explanation.tagged_user_status);
                var statusText = explanation.tagged_user_status_text || explanation.tagged_user_status;
                var panelClass = explanation.tagged_user_status === 'pending' ? 'panel-info' :
                                explanation.tagged_user_status === 'confirmed' ? 'panel-success' : 'panel-danger';

                html += '<div class="panel ' + panelClass + '" style="margin-left: 20px; margin-bottom: 10px;">';
                html += '<div class="panel-heading">';
                html += '<div class="row">';

                // Add checkbox for pending explanations
                if (explanation.tagged_user_status === 'pending') {
                    html += '<div class="col-md-1">';
                    html += '<div class="checkbox-wrapper">';
                    html += '<input type="checkbox" class="explanation-checkbox"';
                    html += ' data-explanation-id="' + explanation.id + '"';
                    html += ' data-user-name="' + creator.name + '"';
                    html += ' id="tagged_explanation_' + explanation.id + '">';
                    html += '<label for="tagged_explanation_' + explanation.id + '" class="checkbox-label">';
                    html += '<i class="fa fa-square-o unchecked"></i>';
                    html += '<i class="fa fa-check-square checked"></i>';
                    html += '</label>';
                    html += '</div>';
                    html += '</div>';
                    html += '<div class="col-md-7">';
                } else {
                    html += '<div class="col-md-8">';
                }

                html += '<h5 class="panel-title">';
                html += '<i class="fa fa-calendar"></i> ' + moment(explanation.date).format('DD/MM/YYYY');
                html += '<small class="text-muted"> - ' + (explanation.explanation_type_text || 'N/A') + '</small>';
                html += '</h5>';
                html += '<small class="text-muted">';
                html += '<i class="fa fa-clock-o"></i> Tạo lúc: ' + moment(explanation.created_at).format('DD/MM/YYYY HH:mm');
                html += '</small>';
                html += '</div>';

                if (explanation.tagged_user_status === 'pending') {
                    html += '<div class="col-md-4 text-right">';
                } else {
                    html += '<div class="col-md-4 text-right">';
                }

                html += '<span class="label label-' + statusClass + '">' + statusText + '</span>';
                html += '</div>';
                html += '</div>';
                html += '</div>';

                html += '<div class="panel-body">';
                html += '<div class="row">';
                html += '<div class="col-md-8">';
                html += '<h6><strong>Nội dung giải trình:</strong></h6>';
                html += '<p class="well well-sm">' + (explanation.explanation || 'N/A') + '</p>';

                if (explanation.explanation_type === 'overtime' && explanation.ot_hours) {
                    html += '<h6><strong>Số giờ OT:</strong></h6>';
                    html += '<p><span class="label label-info">' + explanation.ot_hours + ' giờ</span></p>';
                }

                html += '</div>';
                html += '<div class="col-md-4">';
                html += '<h6><strong>Quy trình xác nhận:</strong></h6>';
                html += '<div class="list-group">';

                if (explanation.tagged_user_status === 'pending') {
                    html += '<div class="list-group-item">';
                    html += '<i class="fa fa-user text-warning"></i>';
                    html += '<strong>Bước 1: Xác nhận</strong>';
                    html += '<br><small class="text-warning">Đang chờ bạn xác nhận</small>';
                    html += '</div>';
                } else {
                    html += '<div class="list-group-item">';
                    html += '<i class="fa fa-user text-success"></i>';
                    html += '<strong>Bước 1: Xác nhận</strong>';
                    html += '<br><small class="text-success">Đã xác nhận</small>';
                    html += '</div>';
                }

                html += '<div class="list-group-item">';
                html += '<i class="fa fa-building text-muted"></i>';
                html += '<strong>Bước 2: Manager</strong>';
                html += '<br><small class="text-muted">Chờ xác nhận trước</small>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '</div>';

                if (explanation.tagged_user_status === 'pending') {
                    html += '<hr>';
                    html += '<div class="row">';
                    html += '<div class="col-md-12">';
                    html += '<h6><strong>Quyết định của bạn:</strong></h6>';
                    html += '<form class="tagged-confirmation-form" data-explanation-id="' + explanation.id + '">';
                    html += '<div class="row">';
                    html += '<div class="col-md-6">';
                    html += '<div class="form-group">';
                    html += '<label>Quyết định:</label>';
                    html += '<select name="status" class="form-control" required>';
                    html += '<option value="">-- Chọn quyết định --</option>';
                    html += '<option value="confirmed">✓ Xác nhận đồng ý</option>';
                    html += '<option value="rejected">✗ Từ chối</option>';
                    html += '</select>';
                    html += '</div>';
                    html += '</div>';
                    html += '<div class="col-md-6">';
                    html += '<div class="form-group">';
                    html += '<label>Ghi chú (tùy chọn):</label>';
                    html += '<textarea name="note" class="form-control" rows="2" placeholder="Ghi chú của bạn..."></textarea>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                    html += '<div class="form-group">';
                    html += '<button type="submit" class="btn btn-primary">';
                    html += '<i class="fa fa-check"></i> Xác nhận quyết định';
                    html += '</button>';
                    html += '</div>';
                    html += '</form>';
                    html += '</div>';
                    html += '</div>';
                }

                html += '</div>';
                html += '</div>';
            });
            html += '</div>';
        });
    }

    $('#explanationsList').html(html);

    // Update pending count badges
    $('#pending-count').text(pendingCount + ' đơn chờ xác nhận');
    $('#pending-tab-count').text(pendingCount);
}

function renderTaggedHistory(explanations) {
    var html = '';

    if (explanations.length === 0) {
        html = '<div class="alert alert-info text-center">';
        html += '<i class="fa fa-info-circle fa-2x"></i>';
        html += '<h4>Chưa có lịch sử xác nhận</h4>';
        html += '<p>Chưa có lịch sử xác nhận giải trình nào.</p>';
        html += '</div>';
    } else {
        // Group by creator
        var groupedByCreator = {};
        explanations.forEach(function(explanation) {
            var creatorId = explanation.created_by;
            var creatorName = explanation.creator_name || 'Unknown';
            if (!groupedByCreator[creatorId]) {
                groupedByCreator[creatorId] = {
                    name: creatorName,
                    explanations: []
                };
            }
            groupedByCreator[creatorId].explanations.push(explanation);
        });

        Object.keys(groupedByCreator).forEach(function(creatorId) {
            var creator = groupedByCreator[creatorId];
            var creatorExplanations = creator.explanations;
            var confirmedCount = creatorExplanations.filter(e => e.tagged_user_status === 'confirmed').length;
            var rejectedCount = creatorExplanations.filter(e => e.tagged_user_status === 'rejected').length;

            // Employee History Header
            html += '<div class="panel panel-info" style="margin-bottom: 5px;">';
            html += '<div class="panel-heading" style="cursor: pointer;" data-toggle="collapse" data-target="#history-' + creatorId + '" aria-expanded="false">';
            html += '<div class="row">';
            html += '<div class="col-md-8">';
            html += '<h5 class="panel-title">';
            html += '<i class="fa fa-user"></i> <strong>' + creator.name + '</strong>';
            html += '</h5>';
            html += '</div>';
            html += '<div class="col-md-4 text-right">';
            if (confirmedCount > 0) {
                html += '<span class="badge" style="background-color: #5cb85c;">' + confirmedCount + ' xác nhận</span>';
            }
            if (rejectedCount > 0) {
                html += '<span class="badge" style="background-color: #d9534f;">' + rejectedCount + ' từ chối</span>';
            }
            html += '<i class="fa fa-chevron-down"></i>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';

            // Employee History Details
            html += '<div id="history-' + creatorId + '" class="panel-collapse collapse">';
            creatorExplanations.forEach(function(explanation) {
                var panelClass = explanation.tagged_user_status === 'confirmed' ? 'panel-success' : 'panel-danger';
                var statusText = explanation.tagged_user_status === 'confirmed' ? 'Đã xác nhận' : 'Đã từ chối';
                var statusClass = explanation.tagged_user_status === 'confirmed' ? 'success' : 'danger';

                html += '<div class="panel ' + panelClass + '" style="margin-left: 20px; margin-bottom: 10px;">';
                html += '<div class="panel-heading">';
                html += '<div class="row">';
                html += '<div class="col-md-8">';
                html += '<h6>';
                html += '<i class="fa fa-calendar"></i> ' + moment(explanation.date).format('DD/MM/YYYY') + ' - ' + (explanation.explanation_type_text || 'N/A');
                html += '<span class="label label-' + statusClass + '">' + statusText + '</span>';
                html += '</h6>';
                html += '<small class="text-muted">';
                html += '<i class="fa fa-check"></i> Xác nhận lúc: ' + moment(explanation.tagged_user_confirmed_at).format('DD/MM/YYYY HH:mm');
                html += '</small>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
                html += '<div class="panel-body">';
                html += '<p><strong>Nội dung:</strong> ' + (explanation.explanation || 'N/A') + '</p>';
                if (explanation.explanation_type === 'overtime' && explanation.ot_hours) {
                    html += '<p><strong>Số giờ OT:</strong> <span class="label label-info">' + explanation.ot_hours + ' giờ</span></p>';
                }
                if (explanation.tagged_user_note) {
                    html += '<p><strong>Ghi chú của bạn:</strong> ' + explanation.tagged_user_note + '</p>';
                }
                html += '<small class="text-muted">Tạo lúc: ' + moment(explanation.created_at).format('DD/MM/YYYY HH:mm') + '</small>';
                html += '</div>';
                html += '</div>';
            });
            html += '</div>';
        });
    }

    $('#historyList').html(html);
}

function getStatusClass(status) {
    switch(status) {
        case 'pending': return 'warning';
        case 'confirmed': return 'success';
        case 'rejected': return 'danger';
        default: return 'default';
    }
}

// Bulk Actions Functionality for Tagged Confirmations
var selectedExplanations = [];

// Checkbox change event
$(document).on('change', '.explanation-checkbox', function() {
    var explanationId = $(this).data('explanation-id');
    var userName = $(this).data('user-name');
    var $panel = $(this).closest('.panel');

    if ($(this).is(':checked')) {
        // Add to selection
        selectedExplanations.push({
            id: explanationId,
            userName: userName,
            element: $panel
        });
        $panel.addClass('explanation-selected');
    } else {
        // Remove from selection
        selectedExplanations = selectedExplanations.filter(function(item) {
            return item.id !== explanationId;
        });
        $panel.removeClass('explanation-selected');
    }

    updateBulkActionsPanel();
});

// Update bulk actions panel
function updateBulkActionsPanel() {
    var count = selectedExplanations.length;
    var $visibleCheckboxes = $('.explanation-checkbox:visible');
    var totalVisible = $visibleCheckboxes.length;

    $('#selectedCount').text(count);

    if (count > 0) {
        $('#bulkActionsPanel').show();
    } else {
        $('#bulkActionsPanel').hide();
    }

    // Update select all button text
    if (count === totalVisible && totalVisible > 0) {
        $('#selectAllBtn').html('<i class="fa fa-minus-square"></i> Bỏ chọn tất cả');
    } else {
        $('#selectAllBtn').html('<i class="fa fa-check-square"></i> Chọn tất cả');
    }
}

// Bulk confirm button
$('#bulkConfirmBtn').on('click', function() {
    if (selectedExplanations.length === 0) {
        alert('Vui lòng chọn ít nhất một giải trình!');
        return;
    }
    showBulkActionModal('confirmed', 'XÁC NHẬN');
});

// Bulk reject button
$('#bulkRejectBtn').on('click', function() {
    if (selectedExplanations.length === 0) {
        alert('Vui lòng chọn ít nhất một giải trình!');
        return;
    }
    showBulkActionModal('rejected', 'TỪ CHỐI');
});

// Select all button
$('#selectAllBtn').on('click', function() {
    var $visibleCheckboxes = $('.explanation-checkbox:visible');
    var allChecked = $visibleCheckboxes.length > 0 && $visibleCheckboxes.filter(':checked').length === $visibleCheckboxes.length;

    if (allChecked) {
        // Nếu tất cả đã được chọn, bỏ chọn tất cả
        $visibleCheckboxes.prop('checked', false).trigger('change');
        $(this).html('<i class="fa fa-check-square"></i> Chọn tất cả');
    } else {
        // Chọn tất cả
        $visibleCheckboxes.prop('checked', true).trigger('change');
        $(this).html('<i class="fa fa-minus-square"></i> Bỏ chọn tất cả');
    }
});

// Clear selection button
$('#clearSelectionBtn').on('click', function() {
    $('.explanation-checkbox').prop('checked', false);
    $('.panel').removeClass('explanation-selected');
    selectedExplanations = [];
    updateBulkActionsPanel();
    $('#selectAllBtn').html('<i class="fa fa-check-square"></i> Chọn tất cả');
});

// Show bulk action modal
function showBulkActionModal(action, actionText) {
    $('#bulkActionType').text(actionText);
    $('#bulkActionCount').text(selectedExplanations.length);

    // Populate list
    var listHtml = '';
    selectedExplanations.forEach(function(item) {
        var dateText = item.element.find('.panel-title').text().trim();

        listHtml += '<div class="list-group-item">';
        listHtml += '<strong>' + item.userName + '</strong>';
        listHtml += '<br><small>' + dateText + '</small>';
        listHtml += '</div>';
    });
    $('#bulkActionList').html(listHtml);

    // Store action for confirmation
    $('#confirmBulkAction').data('action', action);

    $('#bulkActionModal').modal('show');
}

// Confirm bulk action
$('#confirmBulkAction').on('click', function() {
    var action = $(this).data('action');
    var note = $('#bulkNote').val();
    var explanationIds = selectedExplanations.map(function(item) {
        return item.id;
    });

    if (explanationIds.length === 0) {
        alert('Không có giải trình nào được chọn!');
        return;
    }

    var confirmMessage = action === 'confirmed' ?
        'Bạn có chắc chắn muốn XÁC NHẬN ' + explanationIds.length + ' giải trình đã chọn?' :
        'Bạn có chắc chắn muốn TỪ CHỐI ' + explanationIds.length + ' giải trình đã chọn?';

    if (!confirm(confirmMessage)) {
        return;
    }

    // Disable button
    $(this).prop('disabled', true);

    // Simulate bulk action (since we don't have the actual route yet)
    var completedCount = 0;
    var totalCount = explanationIds.length;
    var hasError = false;

    explanationIds.forEach(function(explanationId) {
        $.ajax({
            url: '/attendance-explanation/' + explanationId + '/tagged-user-confirm',
            method: 'POST',
            data: {
                status: action,
                note: note,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                completedCount++;
                if (completedCount === totalCount && !hasError) {
                    showToast('success', 'Thành công!', 'Đã ' + (action === 'confirmed' ? 'xác nhận' : 'từ chối') + ' ' + totalCount + ' giải trình');
                    $('#bulkActionModal').modal('hide');
                    loadTaggedExplanations();
                    loadTaggedHistory();
                }
            },
            error: function(xhr) {
                hasError = true;
                var message = 'Có lỗi xảy ra!';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showToast('error', 'Lỗi!', message);
                $('#confirmBulkAction').prop('disabled', false);
            }
        });
    });
});

// Reset modal when hidden
$('#bulkActionModal').on('hidden.bs.modal', function() {
    $('#bulkNote').val('');
    $('#confirmBulkAction').prop('disabled', false);
});
</script>
<style>
/* Tagged User Employee Card Styles */
.employee-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;
    overflow: hidden;
}

.employee-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.employee-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    cursor: pointer;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #e8e8e8;
}

.tagged-header:hover {
    background: linear-gradient(135deg, #fff3e0 0%, #f8f9fa 100%);
}

.user-header:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
}

.employee-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.employee-avatar {
    margin-right: 16px;
}

.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    text-transform: uppercase;
}

.tagged-avatar {
    background: linear-gradient(135deg, #ff9800, #f57c00) !important;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3) !important;
}

.user-avatar {
    background: linear-gradient(135deg, #2196F3, #1976D2) !important;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3) !important;
}

.employee-details {
    flex: 1;
}

.employee-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.employee-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    color: #7f8c8d;
    font-size: 14px;
}

.employee-account {
    display: flex;
    align-items: center;
    gap: 6px;
}

.employee-account i {
    color: #95a5a6;
}

.employee-stats {
    display: flex;
    align-items: center;
    gap: 16px;
}

.tagged-pending-count {
    text-align: center;
    padding: 8px 16px;
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.user-pending-count {
    text-align: center;
    padding: 8px 16px;
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.count-number {
    display: block;
    font-size: 20px;
    font-weight: bold;
    line-height: 1;
}

.count-label {
    display: block;
    font-size: 12px;
    opacity: 0.9;
}

.expand-icon {
    color: #95a5a6;
    font-size: 16px;
    transition: transform 0.3s ease;
}

.employee-header[aria-expanded="true"] .expand-icon {
    transform: rotate(180deg);
}

/* Legend styles */
.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 5px 0;
}

.legend-icon {
    font-size: 16px;
    margin-right: 8px;
    width: 20px;
    text-align: center;
}

.legend-item .label {
    margin-right: 10px;
    min-width: 120px;
    text-align: center;
}

.legend-desc {
    color: #666;
    font-size: 13px;
    font-style: italic;
}

/* Panel improvements */
.panel-info {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 4px solid #17a2b8;
}

.panel-info:hover {
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.2);
}

.panel-success {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 4px solid #5cb85c;
}

.panel-success:hover {
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(92, 184, 92, 0.2);
}

.panel-danger {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 4px solid #d9534f;
}

.panel-danger:hover {
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(217, 83, 79, 0.2);
}

/* Form improvements */
.tagged-confirmation-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.tagged-confirmation-form .btn {
    border-radius: 20px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tagged-confirmation-form .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Bulk Actions Styles */
.bulk-actions-panel {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border: 1px solid #ffe0b2;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.bulk-info {
    font-size: 16px;
    font-weight: 600;
    color: #e65100;
}

.bulk-info i {
    color: #ff9800;
    margin-right: 8px;
}

.checkbox-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.explanation-checkbox {
    display: none;
}

.checkbox-label {
    cursor: pointer;
    font-size: 18px;
    color: #95a5a6;
    transition: all 0.3s ease;
    margin: 0;
}

.checkbox-label .checked {
    display: none;
    color: #27ae60;
}

.checkbox-label .unchecked {
    display: inline;
}

.explanation-checkbox:checked + .checkbox-label .checked {
    display: inline;
}

.explanation-checkbox:checked + .checkbox-label .unchecked {
    display: none;
}

.checkbox-label:hover {
    color: #3498db;
    transform: scale(1.1);
}

.explanation-checkbox:checked + .checkbox-label:hover {
    color: #27ae60;
}

/* Selected explanation highlight */
.explanation-selected {
    background-color: #fff3e0 !important;
    border-left: 4px solid #ff9800 !important;
}

.explanation-selected .panel-heading {
    background-color: #ffe0b2 !important;
}

/* Responsive */
@media (max-width: 768px) {
    .employee-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
    }

    .employee-info {
        width: 100%;
    }

    .employee-stats {
        width: 100%;
        justify-content: space-between;
    }

    .employee-meta {
        flex-direction: column;
        gap: 8px;
    }

    .avatar-circle {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    .employee-name {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .employee-header {
        padding: 12px;
    }

    .tagged-pending-count, .user-pending-count {
        padding: 6px 12px;
    }

    .count-number {
        font-size: 18px;
    }

    .tagged-confirmation-form {
        padding: 12px;
    }

    .tagged-confirmation-form .btn {
        padding: 6px 16px;
        font-size: 14px;
    }
}
</style>
@endsection
