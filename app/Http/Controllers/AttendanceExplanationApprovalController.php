<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AttendanceExplanation;
use App\Services\AttendanceExplanation\AttendanceExplanationService;

class AttendanceExplanationApprovalController extends Controller
{
    protected $attendanceExplanationService;

    public function __construct(AttendanceExplanationService $attendanceExplanationService)
    {
        $this->attendanceExplanationService = $attendanceExplanationService;
    }

    /**
     * Trang duyệt giải trình cho Manager
     */
    public function managerIndex(Request $request)
    {
        $user = auth()->user();

        // L<PERSON>y danh sách giải trình cần manager duyệt (pending)
        $pendingExplanations = AttendanceExplanation::whereHas('user', function($query) use ($user) {
                $query->whereHas('managers', function($subQuery) use ($user) {
                    $subQuery->where('user_managers.manager_user_id', $user->id)
                             ->whereNull('user_managers.deleted_at');
                });
            })
            ->with(['user.staffDepartment', 'managerApprover', 'hrApprover'])
            ->byManagerStatus('pending')
            ->orderBy('attendance_explanations.created_at', 'desc')
            ->get();

        // Lấy lịch sử giải trình đã duyệt (approved/rejected) - có thể filter theo tháng
        $historyQuery = AttendanceExplanation::whereHas('user', function($query) use ($user) {
                $query->whereHas('managers', function($subQuery) use ($user) {
                    $subQuery->where('user_managers.manager_user_id', $user->id)
                             ->whereNull('user_managers.deleted_at');
                });
            })
            ->with(['user.staffDepartment', 'managerApprover', 'hrApprover'])
            ->whereIn('manager_status', ['approved', 'rejected']);

        // Mặc định lấy 1 tháng gần đây, nhưng có thể mở rộng để filter theo tháng cụ thể
        $historyQuery->where('manager_approved_at', '>=', now()->subMonth());

        $historyExplanations = $historyQuery->orderBy('attendance_explanations.manager_approved_at', 'desc')->get();

        // Nhóm theo user để hiển thị
        $groupedPending = $pendingExplanations->groupBy('user_id');
        $groupedHistory = $historyExplanations->groupBy('user_id');

        // Lấy thống kê
        $stats = [
            'pending_count' => $pendingExplanations->count(),
            'approved_count' => $historyExplanations->where('manager_status', 'approved')->count(),
            'rejected_count' => $historyExplanations->where('manager_status', 'rejected')->count(),
            'users_with_pending' => $groupedPending->count(),
        ];

        // Lấy danh sách tất cả nhân viên được quản lý với thống kê
        $employees = \DB::table('user_managers')
            ->join('users', 'user_managers.user_id', '=', 'users.id')
            ->leftJoin('staff_departments', 'users.staff_department_id', '=', 'staff_departments.id')
            ->where('user_managers.manager_user_id', $user->id)
            ->whereNull('user_managers.deleted_at')
            ->whereNull('users.deleted_at')
            ->select([
                'users.id',
                'users.name',
                'users.account',
                'staff_departments.name as department_name'
            ])
            ->orderBy('users.name')
            ->get();

        // Lấy thống kê giải trình cho từng nhân viên
        $allEmployees = $employees->map(function($employee) {
            $stats = \DB::table('attendance_explanations')
                ->where('user_id', $employee->id)
                ->selectRaw('
                    COUNT(*) as total_count,
                    SUM(CASE WHEN manager_status = "pending" THEN 1 ELSE 0 END) as pending_count,
                    SUM(CASE WHEN manager_status = "approved" THEN 1 ELSE 0 END) as approved_count,
                    SUM(CASE WHEN manager_status = "rejected" THEN 1 ELSE 0 END) as rejected_count,
                    MAX(created_at) as last_explanation_date
                ')
                ->first();

            return (object)[
                'id' => $employee->id,
                'name' => $employee->name,
                'account' => $employee->account,
                'department_name' => $employee->department_name,
                'stats' => $stats
            ];
        });

        return view('pages.attendance-explanation.manager-approval', compact(
            'groupedPending',
            'groupedHistory',
            'stats',
            'allEmployees'
        ));
    }

    /**
     * Trang duyệt giải trình cho HR (chỉ R080)
     */
    public function hrIndex(Request $request)
    {
        $user = auth()->user();

        // Kiểm tra quyền HR (chỉ role R080)
        if (!$user->hasRole('R080')) {
            abort(403, 'Bạn không có quyền truy cập trang này');
        }

        // Lấy danh sách giải trình cần HR duyệt (manager đã duyệt, HR chưa duyệt)
        $pendingExplanations = AttendanceExplanation::with([
                'user.staffDepartment',
                'user.businessDepartment',
                'managerApprover',
                'hrApprover'
            ])
            ->byManagerStatus('approved')
            ->byHrStatus('pending')
            ->orderBy('attendance_explanations.manager_approved_at', 'desc')
            ->get();

        // Lấy lịch sử giải trình đã HR duyệt - có thể filter theo tháng
        $historyQuery = AttendanceExplanation::with([
                'user.staffDepartment',
                'user.businessDepartment',
                'managerApprover',
                'hrApprover'
            ])
            ->byManagerStatus('approved')
            ->whereIn('hr_status', ['approved', 'rejected']);

        // Mặc định lấy 1 tháng gần đây, nhưng có thể mở rộng để filter theo tháng cụ thể
        $historyQuery->where('hr_approved_at', '>=', now()->subMonth());

        $historyExplanations = $historyQuery->orderBy('attendance_explanations.hr_approved_at', 'desc')->get();

        // Group pending explanations by business department (chi nhánh) then by staff department (phòng ban)
        $groupedPending = $pendingExplanations->groupBy(function($explanation) {
            $businessDept = $explanation->user->businessDepartment->name ?? 'Không có chi nhánh';
            $staffDept = $explanation->user->staffDepartment->name ?? 'Không có phòng ban';
            return $businessDept . '|' . $staffDept;
        });

        // Group history explanations by business department then by staff department
        $groupedHistory = $historyExplanations->groupBy(function($explanation) {
            $businessDept = $explanation->user->businessDepartment->name ?? 'Không có chi nhánh';
            $staffDept = $explanation->user->staffDepartment->name ?? 'Không có phòng ban';
            return $businessDept . '|' . $staffDept;
        });

        // Statistics
        $stats = [
            'pending_count' => $pendingExplanations->count(),
            'approved_count' => $historyExplanations->where('hr_status', 'approved')->count(),
            'rejected_count' => $historyExplanations->where('hr_status', 'rejected')->count(),
        ];

        return view('pages.attendance-explanation.hr-approval', compact(
            'groupedPending',
            'groupedHistory',
            'stats'
        ));
    }

    /**
     * API để lấy lịch sử duyệt theo tháng cho Manager
     */
    public function getManagerHistoryByMonth(Request $request)
    {
        $user = auth()->user();
        $month = $request->input('month', date('n'));
        $year = $request->input('year', date('Y'));

        $historyExplanations = AttendanceExplanation::whereHas('user', function($query) use ($user) {
                $query->whereHas('managers', function($subQuery) use ($user) {
                    $subQuery->where('user_managers.manager_user_id', $user->id)
                             ->whereNull('user_managers.deleted_at');
                });
            })
            ->with(['user.staffDepartment', 'managerApprover', 'hrApprover'])
            ->whereIn('manager_status', ['approved', 'rejected'])
            ->whereYear('manager_approved_at', $year)
            ->whereMonth('manager_approved_at', $month)
            ->orderBy('attendance_explanations.manager_approved_at', 'desc')
            ->get();

        $groupedHistory = $historyExplanations->groupBy('user_id');

        return response()->json([
            'success' => true,
            'data' => $groupedHistory,
            'month' => $month,
            'year' => $year,
            'count' => $historyExplanations->count()
        ]);
    }

    /**
     * API để lấy lịch sử duyệt theo tháng cho HR
     */
    public function getHrHistoryByMonth(Request $request)
    {
        $user = auth()->user();

        // Kiểm tra quyền HR
        if (!$user->hasRole('R080')) {
            return response()->json(['success' => false, 'message' => 'Không có quyền truy cập'], 403);
        }

        $month = $request->input('month', date('n'));
        $year = $request->input('year', date('Y'));

        $historyExplanations = AttendanceExplanation::with([
                'user.staffDepartment',
                'user.businessDepartment',
                'managerApprover',
                'hrApprover'
            ])
            ->byManagerStatus('approved')
            ->whereIn('hr_status', ['approved', 'rejected'])
            ->whereYear('hr_approved_at', $year)
            ->whereMonth('hr_approved_at', $month)
            ->orderBy('attendance_explanations.hr_approved_at', 'desc')
            ->get();

        // Group by business department then by staff department
        $groupedHistory = $historyExplanations->groupBy(function($explanation) {
            $businessDept = $explanation->user->businessDepartment->name ?? 'Không có chi nhánh';
            $staffDept = $explanation->user->staffDepartment->name ?? 'Không có phòng ban';
            return $businessDept . '|' . $staffDept;
        });

        return response()->json([
            'success' => true,
            'data' => $groupedHistory,
            'month' => $month,
            'year' => $year,
            'count' => $historyExplanations->count()
        ]);
    }

    /**
     * API để lấy chi tiết giải trình của một user
     */
    public function getUserExplanations(Request $request, $userId)
    {
        $user = auth()->user();

        // Kiểm tra quyền xem giải trình của user này
        $canView = \DB::table('user_managers')
            ->where('user_id', $userId)
            ->where('manager_user_id', $user->id)
            ->whereNull('deleted_at')
            ->exists();

        if (!$canView) {
            return response()->json(['error' => 'Không có quyền xem'], 403);
        }

        $explanations = AttendanceExplanation::where('user_id', $userId)
            ->with(['user.staffDepartment', 'managerApprover', 'hrApprover'])
            ->orderBy('created_at', 'desc')
            ->limit(50) // Giới hạn 50 bản ghi gần nhất
            ->get();

        return response()->json([
            'success' => true,
            'data' => $explanations
        ]);
    }

    /**
     * API để lấy danh sách tất cả nhân viên mà manager quản lý
     */
    public function getAllManagedEmployees(Request $request)
    {
        $user = auth()->user();

        // Lấy danh sách nhân viên mà manager quản lý
        $employees = \DB::table('user_managers')
            ->join('users', 'user_managers.user_id', '=', 'users.id')
            ->leftJoin('staff_departments', 'users.staff_department_id', '=', 'staff_departments.id')
            ->where('user_managers.manager_user_id', $user->id)
            ->whereNull('user_managers.deleted_at')
            ->whereNull('users.deleted_at')
            ->select([
                'users.id',
                'users.name',
                'users.account',
                'staff_departments.name as department_name'
            ])
            ->orderBy('users.name')
            ->get();

        // Lấy thống kê giải trình cho từng nhân viên
        $employeesWithStats = $employees->map(function($employee) {
            $stats = \DB::table('attendance_explanations')
                ->where('user_id', $employee->id)
                ->selectRaw('
                    COUNT(*) as total_count,
                    SUM(CASE WHEN manager_status = "pending" THEN 1 ELSE 0 END) as pending_count,
                    SUM(CASE WHEN manager_status = "approved" THEN 1 ELSE 0 END) as approved_count,
                    SUM(CASE WHEN manager_status = "rejected" THEN 1 ELSE 0 END) as rejected_count,
                    MAX(created_at) as last_explanation_date
                ')
                ->first();

            return [
                'id' => $employee->id,
                'name' => $employee->name,
                'account' => $employee->account,
                'department_name' => $employee->department_name,
                'stats' => $stats
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $employeesWithStats
        ]);
    }

    /**
     * Bulk approve/reject explanations by manager
     */
    public function bulkManagerApprove(Request $request)
    {
        $request->validate([
            'explanation_ids' => 'required|array|min:1',
            'explanation_ids.*' => 'required|integer|exists:attendance_explanations,id',
            'status' => 'required|in:approved,rejected',
            'note' => 'nullable|string|max:1000'
        ]);

        $user = auth()->user();
        $explanationIds = $request->explanation_ids;
        $status = $request->status;
        $note = $request->note;

        try {
            \DB::beginTransaction();

            // Lấy các giải trình cần duyệt
            $explanations = \DB::table('attendance_explanations')
                ->join('user_managers', 'attendance_explanations.user_id', '=', 'user_managers.user_id')
                ->where('user_managers.manager_user_id', $user->id)
                ->whereIn('attendance_explanations.id', $explanationIds)
                ->where('attendance_explanations.manager_status', 'pending')
                ->whereNull('user_managers.deleted_at')
                ->select('attendance_explanations.*')
                ->get();

            if ($explanations->count() !== count($explanationIds)) {
                throw new \Exception('Một số giải trình không tồn tại hoặc bạn không có quyền duyệt');
            }

            $processedCount = 0;
            $now = now();

            foreach ($explanations as $explanation) {
                // Cập nhật trạng thái manager
                \DB::table('attendance_explanations')
                    ->where('id', $explanation->id)
                    ->update([
                        'manager_status' => $status,
                        'manager_approved_by' => $user->id,
                        'manager_approved_at' => $now,
                        'manager_note' => $note,
                        'updated_at' => $now
                    ]);

                // Nếu manager từ chối, cập nhật final_status
                if ($status === 'rejected') {
                    \DB::table('attendance_explanations')
                        ->where('id', $explanation->id)
                        ->update([
                            'final_status' => 'rejected',
                            'updated_at' => $now
                        ]);
                }

                $processedCount++;
            }

            \DB::commit();

            $actionText = $status === 'approved' ? 'duyệt' : 'từ chối';
            $message = "Đã {$actionText} thành công {$processedCount} giải trình";

            return response()->json([
                'success' => true,
                'message' => $message,
                'processed_count' => $processedCount
            ]);

        } catch (\Exception $e) {
            \DB::rollback();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Bulk approve/reject explanations by HR
     */
    public function bulkHrApprove(Request $request)
    {
        $request->validate([
            'explanation_ids' => 'required|array|min:1',
            'explanation_ids.*' => 'required|integer|exists:attendance_explanations,id',
            'status' => 'required|in:approved,rejected',
            'note' => 'nullable|string|max:1000'
        ]);

        $user = auth()->user();

        // Kiểm tra quyền HR (chỉ role R080)
        if (!$user->hasRole('R080')) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền truy cập chức năng này'
            ], 403);
        }

        $explanationIds = $request->explanation_ids;
        $status = $request->status;
        $note = $request->note;

        try {
            \DB::beginTransaction();

            // Lấy các giải trình cần duyệt
            $explanations = AttendanceExplanation::whereIn('id', $explanationIds)
                ->byManagerStatus('approved') // Chỉ duyệt những cái manager đã approve
                ->byHrStatus('pending') // Chỉ duyệt những cái HR chưa duyệt
                ->get();

            if ($explanations->count() !== count($explanationIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Một số giải trình không hợp lệ hoặc đã được xử lý'
                ], 400);
            }

            $processedCount = 0;
            $now = now();

            foreach ($explanations as $explanation) {
                // Cập nhật trạng thái HR
                \DB::table('attendance_explanations')
                    ->where('id', $explanation->id)
                    ->update([
                        'hr_status' => $status,
                        'hr_approved_by' => $user->id,
                        'hr_approved_at' => $now,
                        'hr_note' => $note,
                        'final_status' => $status, // HR là bước cuối nên final_status = hr_status
                        'updated_at' => $now
                    ]);

                $processedCount++;
            }

            \DB::commit();

            $actionText = $status === 'approved' ? 'duyệt' : 'từ chối';
            $message = "Đã {$actionText} thành công {$processedCount} giải trình";

            return response()->json([
                'success' => true,
                'message' => $message,
                'processed_count' => $processedCount
            ]);

        } catch (\Exception $e) {
            \DB::rollback();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
