{"R001": {"id": 1, "name": "R001", "display_name": "Administrator", "permissions": ["viewAny booking", "update booking", "viewAny booking_source", "create booking_source", "update booking_source", "delete booking_source", "restore booking_source", "viewPhoneNumber customer", "viewCustomerRevenues customer", "viewAny shop", "create shop", "update shop", "delete shop", "restore shop", "viewAny team", "create team", "restore team", "viewAny user", "create user", "update user", "delete user", "import user", "viewRevenue report", "viewAny product", "create product", "update product", "delete product", "restore product", "viewAny department", "create department", "update department", "delete department", "restore department", "viewAny customer_revenue", "update customer_revenue", "delete customer_revenue", "viewAny marketing_team", "create marketing_team", "update marketing_team", "delete marketing_team", "restore marketing_team", "viewAny lead_report", "update lead_report", "viewAny customer", "update customer", "viewAny doctor", "create doctor", "update doctor", "delete doctor", "restore doctor", "viewAny lead_report_doctor", "update lead_report_doctor", "create customer", "create fast_filter_lead_report", "delete fast_filter_lead_report", "updatePhoneNumber customer", "viewAny staff_department", "create staff_department", "update staff_department", "delete staff_department", "viewAny company_address", "create company_address", "update company_address", "delete company_address", "import customer", "viewReportAll lead_report", "delete booking", "activate user", "viewAny shift", "create shift", "update shift", "delete shift", "viewAny position", "create position", "update position", "delete position", "viewReportDepartment lead_report", "viewReportProduct lead_report", "viewReportDoctorAll lead_report", "viewAny role", "create role", "update role", "delete role", "viewAny notice", "create notice", "update notice", "delete notice", "viewAny booking_history", "viewAny customer_survey", "create customer_survey", "update customer_survey", "delete customer_survey", "viewAny sale_report", "update sale_report", "viewReport sale_report", "viewComparisonReportShop lead_report", "viewComparisonReportMarketingTeam lead_report", "viewComparisonReportBusinessDepartment lead_report", "viewAny shop_kpi", "create shop_kpi", "update shop_kpi", "delete shop_kpi", "viewAny marketing_team_kpi", "create marketing_team_kpi", "update marketing_team_kpi", "delete marketing_team_kpi", "viewKpiChartShop kpi", "viewKpiChartMarketingTeam kpi", "setShowRealMoney user", "viewAny marketing_team_filter", "create marketing_team_filter", "update marketing_team_filter", "delete marketing_team_filter", "viewAny rating", "create rating", "update rating", "delete rating", "verify rating", "updateIn24Hours rating", "deleteIn24Hours rating", "viewStatistics rating", "viewAny shift_rule_history", "create shift_rule_history", "update shift_rule_history", "delete shift_rule_history", "viewReportService lead_report"], "permissions_count": 126}, "R002": {"id": 2, "name": "R002", "display_name": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>h do<PERSON>h", "permissions": ["viewAny lead_report", "viewAny lead_report_doctor", "viewReportAll lead_report", "viewReportDoctorAll lead_report"], "permissions_count": 4}, "R003": {"id": 3, "name": "R003", "display_name": "<PERSON><PERSON><PERSON><PERSON> đốc chi n<PERSON>h", "permissions": ["viewAny booking", "create booking", "update booking", "viewPhoneNumber customer", "viewRevenue report", "create fast_filter_lead_report", "delete fast_filter_lead_report", "viewReportDepartment lead_report", "viewReportProduct lead_report", "viewRevenueHeadEvaluation booking", "listenBookingAudio booking", "limitByShop booking", "limitByShop customer", "limitByShop lead_report"], "permissions_count": 14}, "R010": {"id": 4, "name": "R010", "display_name": "<PERSON><PERSON><PERSON><PERSON> lý telesale", "permissions": ["viewAny booking", "viewAny user", "create fast_filter_lead_report", "delete fast_filter_lead_report", "viewPhoneNumberFirstFiveHidden customer", "viewAny sale_report", "create sale_report", "update sale_report", "viewReport sale_report", "restrictAsSalesmanManager sale_report", "listenBookingAudio booking", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer"], "permissions_count": 13}, "R011": {"id": 5, "name": "R011", "display_name": "Telesale", "permissions": ["viewAny booking", "create booking", "update booking", "viewPhoneNumber customer", "create fast_filter_lead_report", "delete fast_filter_lead_report", "viewAny sale_report", "create sale_report", "update sale_report", "viewReport sale_report", "restrictAsSalesman sale_report", "viewBookingLast2Months booking"], "permissions_count": 12}, "R021": {"id": 6, "name": "R021", "display_name": "<PERSON><PERSON> toán", "permissions": ["viewAny booking", "viewCustomerRevenues customer", "create customer_revenue", "viewRevenue report", "viewAny customer_revenue", "update customer_revenue", "delete customer_revenue", "viewAny customer", "create customer", "create fast_filter_lead_report", "delete fast_filter_lead_report", "viewPhoneNumberFirstFiveHidden customer", "viewCustomerRevenueLast2Months customer_revenue", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByShop booking", "limitByShop customer", "limitByShop customer_revenue"], "permissions_count": 18}, "R031": {"id": 7, "name": "R031", "display_name": "<PERSON><PERSON>", "permissions": ["viewAny booking", "create booking", "update booking", "create fast_filter_lead_report", "delete fast_filter_lead_report", "viewPhoneNumberFirstFiveHidden customer", "listenBookingAudio booking", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByShop booking", "limitByShop customer"], "permissions_count": 11}, "R020": {"id": 9, "name": "R020", "display_name": "<PERSON><PERSON> toán Back Office", "permissions": ["viewAny booking", "viewCustomerRevenues customer", "create customer_revenue", "viewAny customer_revenue", "update customer_revenue", "delete customer_revenue", "viewAny lead_report", "update lead_report", "create customer", "create fast_filter_lead_report", "delete fast_filter_lead_report", "viewReportDepartment lead_report", "viewReportProduct lead_report", "viewPhoneNumberFirstFiveHidden customer", "viewReportLast2Months lead_report", "viewCustomerRevenueLast2Months customer_revenue", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByShop booking", "limitByShop customer", "limitByShop customer_revenue", "limitByShop lead_report"], "permissions_count": 22}, "R051": {"id": 10, "name": "R051", "display_name": "<PERSON><PERSON><PERSON> s<PERSON> k<PERSON>ch hàng", "permissions": ["viewAny booking", "viewAny customer_revenue", "viewAny customer", "update customer", "createReExamination booking", "create customer", "create fast_filter_lead_report", "delete fast_filter_lead_report", "editReExamination booking", "viewPhoneNumberFirstFiveHidden customer", "viewCustomerRevenueLast2Months customer_revenue", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByShop booking", "limitByShop customer", "limitByShop customer_revenue"], "permissions_count": 16}, "R061": {"id": 11, "name": "R061", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "permissions": ["viewAny booking", "update booking", "viewPhoneNumber customer", "viewAny customer_revenue", "viewAny lead_report", "create lead_report", "update lead_report", "createReExamination booking", "viewAny lead_report_doctor", "create lead_report_doctor", "update lead_report_doctor", "create fast_filter_lead_report", "delete fast_filter_lead_report", "delete booking", "editReExamination booking", "viewReportDepartment lead_report", "viewReportProduct lead_report", "viewAny customer_survey", "create customer_survey", "update customer_survey", "delete customer_survey", "viewAny shop_kpi", "create shop_kpi", "update shop_kpi", "delete shop_kpi", "viewAny marketing_team_kpi", "create marketing_team_kpi", "update marketing_team_kpi", "delete marketing_team_kpi", "viewReportLast2Months lead_report", "viewCustomerRevenueLast2Months customer_revenue", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByMktTeam lead_report", "limitByShop lead_report", "viewAndCreateAndUpdateLimitByShop lead_report"], "permissions_count": 36}, "R070": {"id": 12, "name": "R070", "display_name": "TP MTK", "permissions": ["viewAny booking", "viewAny customer_revenue", "create fast_filter_lead_report", "delete fast_filter_lead_report", "viewReportDepartment lead_report", "viewPhoneNumberFirstFiveHidden customer", "viewRevenueHeadEvaluation booking", "requireSpecificDoctors doctor", "requireSpecificDoctorsToBooking doctor", "requireSpecificDoctorsToLeadReport doctor", "listenBookingAudio booking", "viewReportLast2Months lead_report", "viewCustomerRevenueLast2Months customer_revenue", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByMktTeam booking", "limitByMktTeam customer_revenue", "limitByMktTeam lead_report"], "permissions_count": 18}, "R000": {"id": 13, "name": "R000", "display_name": "Super administrator", "permissions": ["viewAny booking", "update booking", "viewAny booking_source", "create booking_source", "update booking_source", "delete booking_source", "restore booking_source", "viewPhoneNumber customer", "viewCustomerRevenues customer", "viewAny shop", "create shop", "update shop", "delete shop", "restore shop", "viewAny team", "create team", "restore team", "viewAny user", "create user", "update user", "delete user", "import user", "viewRevenue report", "viewAny product", "create product", "update product", "delete product", "restore product", "viewAny department", "create department", "update department", "delete department", "restore department", "viewAny customer_revenue", "update customer_revenue", "delete customer_revenue", "viewAny marketing_team", "create marketing_team", "update marketing_team", "delete marketing_team", "restore marketing_team", "viewAny lead_report", "update lead_report", "viewAny customer", "update customer", "viewAny doctor", "create doctor", "update doctor", "delete doctor", "restore doctor", "viewAny lead_report_doctor", "update lead_report_doctor", "create customer", "create fast_filter_lead_report", "delete fast_filter_lead_report", "updatePhoneNumber customer", "viewAny staff_department", "create staff_department", "update staff_department", "delete staff_department", "viewAny company_address", "create company_address", "update company_address", "delete company_address", "import customer", "viewReportAll lead_report", "delete booking", "activate user", "viewAny shift", "create shift", "update shift", "delete shift", "viewAny position", "create position", "update position", "delete position", "viewReportDepartment lead_report", "viewReportProduct lead_report", "viewReportDoctorAll lead_report", "viewAny role", "create role", "update role", "delete role", "viewAny notice", "create notice", "update notice", "delete notice", "viewAny booking_history", "viewAny customer_survey", "create customer_survey", "update customer_survey", "delete customer_survey", "viewAny sale_report", "update sale_report", "viewReport sale_report", "viewComparisonReportShop lead_report", "viewComparisonReportMarketingTeam lead_report", "viewComparisonReportBusinessDepartment lead_report", "viewAny shop_kpi", "create shop_kpi", "update shop_kpi", "delete shop_kpi", "viewAny marketing_team_kpi", "create marketing_team_kpi", "update marketing_team_kpi", "delete marketing_team_kpi", "viewKpiChartShop kpi", "viewKpiChartMarketingTeam kpi", "setShowRealMoney user", "viewAny marketing_team_filter", "create marketing_team_filter", "update marketing_team_filter", "delete marketing_team_filter", "viewAny rating", "create rating", "update rating", "delete rating", "verify rating", "updateIn24Hours rating", "deleteIn24Hours rating", "viewStatistics rating", "viewAny shift_rule_history", "create shift_rule_history", "update shift_rule_history", "delete shift_rule_history", "viewReportService lead_report"], "permissions_count": 126}, "R071": {"id": 14, "name": "R071", "display_name": "<PERSON><PERSON> thuật viên", "permissions": ["viewAny booking", "update booking", "viewPhoneNumberFirstFiveHidden customer", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByShop booking", "limitByShop customer"], "permissions_count": 7}, "R072": {"id": 15, "name": "R072", "display_name": "Upsale", "permissions": ["viewAny booking", "update booking", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByShop booking", "limitByShop customer"], "permissions_count": 6}, "R080": {"id": 17, "name": "R080", "display_name": "<PERSON><PERSON><PERSON> ch<PERSON>h nhân sự", "permissions": ["viewAny user", "create user", "update user", "delete user", "import user", "viewAny timesheet", "create timesheet", "update timesheet", "delete timesheet", "export timesheet", "viewAny shift", "create shift", "update shift", "delete shift", "viewAny notice", "create notice", "update notice", "delete notice"], "permissions_count": 18}, "R090": {"id": 18, "name": "R090", "display_name": "Nhân viên Back Office", "permissions": [], "permissions_count": 0}, "R110": {"id": 21, "name": "R110", "display_name": "<PERSON><PERSON><PERSON><PERSON> xem lịch hẹn theo bác sĩ chỉ định", "permissions": ["viewAny booking", "requireSpecificDoctorsToBooking doctor", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByMktTeam booking"], "permissions_count": 5}, "R022": {"id": 22, "name": "R022", "display_name": "Kế toán trưởng", "permissions": ["create customer_revenue", "viewRevenue report", "viewAny customer_revenue", "viewReportAll lead_report", "viewReportDepartment lead_report", "viewReportProduct lead_report", "viewReportDoctorAll lead_report", "viewRevenueHeadEvaluation booking"], "permissions_count": 8}, "R005": {"id": 24, "name": "R005", "display_name": "Giám s<PERSON>t <PERSON>", "permissions": ["viewAny booking", "viewPhoneNumberFirstFiveHidden customer", "listenBookingAudio booking", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer"], "permissions_count": 5}, "R006": {"id": 26, "name": "R006", "display_name": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t chất l<PERSON>", "permissions": ["viewAny rating", "create rating", "update rating", "delete rating", "verify rating", "updateIn24Hours rating", "deleteIn24Hours rating", "viewStatistics rating", "viewReportService lead_report"], "permissions_count": 9}, "R111": {"id": 27, "name": "R111", "display_name": "<PERSON><PERSON><PERSON>i xem báo cáo LEAD bác sĩ chỉ định", "permissions": ["viewReportDepartment lead_report", "requireSpecificDoctorsToLeadReport doctor", "viewReportLast2Months lead_report", "limitByMktTeam lead_report"], "permissions_count": 4}, "R007": {"id": 28, "name": "R007", "display_name": "XEM DS CHẤM CÔNG", "permissions": ["viewAny timesheet", "export timesheet", "viewAny shift"], "permissions_count": 3}, "R004": {"id": 29, "name": "R004", "display_name": "Giám đốc Marketing", "permissions": ["viewAny booking", "viewAny customer_revenue", "create fast_filter_lead_report", "delete fast_filter_lead_report", "viewReportDepartment lead_report", "viewPhoneNumberFirstFiveHidden customer", "viewRevenueHeadEvaluation booking", "exportBooking booking", "listenBookingAudio booking"], "permissions_count": 9}, "R008": {"id": 30, "name": "R008", "display_name": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t c<PERSON> sở", "permissions": ["viewAny booking", "viewAny customer_revenue", "viewAny customer", "viewPhoneNumberFirstFiveHidden customer", "exportBooking booking", "listenBookingAudio booking", "viewCustomerRevenueLast2Months customer_revenue", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByShop booking", "limitByShop customer", "limitByShop customer_revenue"], "permissions_count": 12}, "R073": {"id": 31, "name": "R073", "display_name": "no rule", "permissions": [], "permissions_count": 0}, "R009": {"id": 33, "name": "R009", "display_name": "Trưởng phòng giám sát", "permissions": ["viewAny booking", "viewPhoneNumber customer", "viewAny lead_report", "viewAny customer", "viewReportDepartment lead_report", "viewReportProduct lead_report", "viewReportDoctorAll lead_report", "viewAny customer_survey", "create customer_survey", "update customer_survey", "delete customer_survey", "exportBooking booking", "listenBookingAudio booking", "viewReportLast2Months lead_report", "limitByShop customer_survey", "manage shop_2_kpi"], "permissions_count": 16}, "R012": {"id": 34, "name": "R012", "display_name": "<PERSON><PERSON> l<PERSON> hẹn", "permissions": ["viewAny booking", "listenBookingAudio booking", "viewBookingLast2Months booking", "limitByShop booking"], "permissions_count": 4}, "R112": {"id": 35, "name": "R112", "display_name": "Lead-MKT", "permissions": ["viewAny booking", "viewReportDepartment lead_report", "requireSpecificDoctors doctor", "requireSpecificDoctorsToBooking doctor", "requireSpecificDoctorsToLeadReport doctor", "viewReportLast2Months lead_report", "viewBookingLast2Months booking", "limitByMktTeam booking", "limitByMktTeam lead_report"], "permissions_count": 9}, "R113": {"id": 36, "name": "R113", "display_name": "TP-SALE", "permissions": ["viewAny booking", "create fast_filter_lead_report", "delete fast_filter_lead_report", "viewReportDepartment lead_report", "viewPhoneNumberFirstFiveHidden customer", "viewAny sale_report", "create sale_report", "update sale_report", "viewReport sale_report", "restrictAsSalesmanManager sale_report", "listenBookingAudio booking", "viewReportLast2Months lead_report", "viewBookingLast2Months booking", "viewIdentityNumberOnlyLastFour customer", "limitByMktTeam booking", "limitByMktTeam lead_report"], "permissions_count": 16}}