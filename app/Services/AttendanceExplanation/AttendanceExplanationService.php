<?php

namespace App\Services\AttendanceExplanation;

use App\Models\User;
use App\Models\Timesheet;
use App\Models\AttendanceExplanation;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class AttendanceExplanationService
{
    /**
     * Lấy dữ liệu chấm công cho tháng (cho API - chỉ ngày trong tháng)
     */
    public function getMonthlyAttendance(User $user, Carbon $month): Collection
    {
        // Chỉ lấy range trong tháng hiện tại (cho API)
        $startDate = $month->copy()->startOfMonth();
        $endDate = $month->copy()->endOfMonth();

        return $this->getAttendanceDataInRange($user, $startDate, $endDate);
    }

    /**
     * Lấy dữ liệu chấm công cho calendar view (bao gồm các ngày của tháng trước/sau)
     */
    public function getMonthlyAttendanceForCalendar(User $user, Carbon $month): Collection
    {
        // Lấy range cho toàn bộ calendar (bao gồm ngày của tháng trước/sau)
        $startDate = $month->copy()->startOfMonth()->startOfWeek(Carbon::MONDAY);
        $endDate = $month->copy()->endOfMonth()->endOfWeek(Carbon::SUNDAY);

        return $this->getAttendanceDataInRange($user, $startDate, $endDate);
    }

    /**
     * Lấy dữ liệu chấm công trong khoảng thời gian
     */
    private function getAttendanceDataInRange(User $user, Carbon $startDate, Carbon $endDate): Collection
    {
        // Lấy tất cả timesheets trong khoảng thời gian (lấy timesheet mới nhất nếu có duplicate)
        $timesheets = Timesheet::where('user_id', $user->id)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->with(['shift', 'shiftRuleHistory'])
            ->orderBy('date')
            ->orderBy('id', 'desc') // Lấy timesheet mới nhất nếu có duplicate
            ->get()
            ->groupBy(function($item) {
                return $item->date->format('Y-m-d');
            })
            ->map(function($group) {
                return $group->first(); // Lấy timesheet đầu tiên (mới nhất do orderBy id desc)
            });

        // Lấy tất cả giải trình trong tháng
        $explanations = AttendanceExplanation::where('user_id', $user->id)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->get()
            ->groupBy(function($item) {
                return $item->date->format('Y-m-d');
            });

        // Lấy tổng số giờ OT đã được duyệt cho từng ngày
        $approvedOtHours = AttendanceExplanation::where('user_id', $user->id)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->where('explanation_type', 'overtime')
            ->where('manager_status', 'approved')
            ->where('hr_status', 'approved')
            ->whereNotNull('ot_hours')
            ->selectRaw('date, SUM(ot_hours) as total_ot_hours')
            ->groupBy('date')
            ->pluck('total_ot_hours', 'date');

        // Tạo collection cho tất cả ngày trong tháng
        $result = collect();
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $dateKey = $currentDate->format('Y-m-d');
            $timesheet = $timesheets->get($dateKey);
            $dayExplanations = $explanations->get($dateKey, collect());



            // Lấy số giờ OT đã được duyệt cho ngày này
            $approvedOtForDay = $approvedOtHours->get($dateKey, 0);

            if ($timesheet) {
                // Có dữ liệu timesheet - sử dụng logic mới với điều chỉnh
                $attendanceData = $this->calculateAttendanceStatus($timesheet, $dayExplanations);
                $attendanceData['explanations'] = $dayExplanations;
                $attendanceData['has_explanation'] = $dayExplanations->count() > 0;
                $attendanceData['explanation_count'] = $dayExplanations->count();

                // Cập nhật status color dựa trên trạng thái điều chỉnh
                if ($attendanceData['is_adjusted']) {
                    $attendanceData['status_color'] = 'success';
                    $attendanceData['status_text'] = 'Đã điều chỉnh';
                } elseif ($dayExplanations->count() > 0) {
                    $attendanceData['status_color'] = 'info';
                    $attendanceData['status_text'] = 'Đã giải trình';
                }
            } else {
                // Không có dữ liệu timesheet
                $finalOtHours = $this->getFinalOtHours($dayExplanations);

                $attendanceData = [
                    'date' => $dateKey,
                    'checkin' => null,
                    'checkout' => null,
                    'shift_name' => null,
                    'schedule_start' => null,
                    'schedule_end' => null,
                    'original_workday' => 0,
                    'workday' => 0,
                    'work_hours' => 0,
                    'real_hours' => 0,
                    'late_minutes' => 0,
                    'early_minutes' => 0,
                    'status' => 'no_data',
                    'status_text' => $dayExplanations->count() > 0 ? 'Đã giải trình' : 'Chưa chấm công',
                    'status_color' => $dayExplanations->count() > 0 ? 'info' : 'default',
                    'explanations' => $dayExplanations,
                    'has_explanation' => $dayExplanations->count() > 0,
                    'explanation_count' => $dayExplanations->count(),
                    'approved_ot_hours' => $finalOtHours,
                    'is_adjusted' => $finalOtHours > 0,
                    'adjustment_reason' => $this->getAdjustmentReason($dayExplanations),
                ];
            }

            $result->put($dateKey, $attendanceData);
            $currentDate->addDay();
        }

        return $result;
    }

    /**
     * Lấy thông tin chấm công cho một ngày cụ thể
     */
    public function getDayAttendance(User $user, Carbon $date): ?array
    {
        $dateKey = $date->format('Y-m-d');

        $timesheet = Timesheet::where('user_id', $user->id)
            ->where('date', $dateKey)
            ->with(['shift', 'shiftRuleHistory'])
            ->orderBy('id', 'desc') // Lấy timesheet mới nhất nếu có duplicate
            ->first();

        // Lấy giải trình cho ngày này với relationship
        $explanations = AttendanceExplanation::where('user_id', $user->id)
            ->where('date', $dateKey)
            ->with(['remoteShift', 'managerApprover', 'hrApprover', 'taggedUser'])
            ->get();

        // Lấy tổng số giờ OT đã được duyệt cho ngày này
        $approvedOtHours = AttendanceExplanation::where('user_id', $user->id)
            ->where('date', $dateKey)
            ->where('explanation_type', 'overtime')
            ->where('manager_status', 'approved')
            ->where('hr_status', 'approved')
            ->whereNotNull('ot_hours')
            ->sum('ot_hours');

        if ($timesheet) {
            // Có dữ liệu timesheet - sử dụng logic mới với điều chỉnh
            $attendanceData = $this->calculateAttendanceStatus($timesheet, $explanations);
        } else {
            // Không có timesheet
            $finalOtHours = $this->getFinalOtHours($explanations);

            $attendanceData = [
                'date' => $dateKey,
                'checkin' => null,
                'checkout' => null,
                'shift_name' => null,
                'schedule_start' => null,
                'schedule_end' => null,
                'original_workday' => 0,
                'workday' => 0,
                'work_hours' => 0,
                'real_hours' => 0,
                'late_minutes' => 0,
                'early_minutes' => 0,
                'status' => 'no_data',
                'status_text' => 'Chưa chấm công',
                'status_color' => 'default',
                'approved_ot_hours' => $finalOtHours,
                'is_adjusted' => $finalOtHours > 0,
                'adjustment_reason' => $this->getAdjustmentReason($explanations),
            ];
        }

        // Thêm thông tin giải trình với can_update và tagged user info
        $explanationsWithPermissions = $explanations->map(function($explanation) use ($user) {
            $canUpdate = $this->canUpdateExplanation($user, $explanation);
            $explanation->can_update = $canUpdate['allowed'];

            // Thêm thông tin tagged user
            if ($explanation->taggedUser) {
                $explanation->tagged_user_name = $explanation->taggedUser->name;
            }

            return $explanation;
        });

        $attendanceData['explanations'] = $explanationsWithPermissions;
        $attendanceData['has_explanation'] = $explanations->count() > 0;
        $attendanceData['explanation_count'] = $explanations->count();

        // Cập nhật status color dựa trên trạng thái điều chỉnh
        if ($attendanceData['is_adjusted']) {
            $attendanceData['status_color'] = 'success';
            $attendanceData['status_text'] = 'Đã điều chỉnh';
        } elseif ($explanations->count() > 0) {
            $attendanceData['status_color'] = 'info';
            $attendanceData['status_text'] = 'Đã giải trình';
        }

        return $attendanceData;
    }

    /**
     * Tính toán trạng thái chấm công cho một timesheet (với giải trình)
     */
    protected function calculateAttendanceStatus(Timesheet $timesheet, Collection $explanations = null): array
    {
        $explanations = $explanations ?? collect();

        // Tính toán dữ liệu gốc
        $originalData = $this->calculateOriginalAttendanceStatus($timesheet);

        // Áp dụng điều chỉnh từ giải trình đã duyệt
        $finalWorkday = $this->getFinalWorkday($timesheet, $explanations);
        $finalOtHours = $this->getFinalOtHours($explanations);

        return array_merge($originalData, [
            'original_workday' => $originalData['workday'],
            'workday' => $finalWorkday,
            'approved_ot_hours' => $finalOtHours,
            'is_adjusted' => $finalWorkday !== $originalData['workday'] || $finalOtHours > 0,
            'adjustment_reason' => $this->getAdjustmentReason($explanations),
        ]);
    }

    /**
     * Tính toán trạng thái chấm công gốc (không có điều chỉnh)
     */
    protected function calculateOriginalAttendanceStatus(Timesheet $timesheet): array
    {
        $shift = $timesheet->shift;
        $shiftRule = $timesheet->shiftRuleHistory;

        if (!$shift) {
            return [
                'date' => $timesheet->date,
                'checkin' => $timesheet->checkin,
                'checkout' => $timesheet->checkout,
                'shift_name' => null,
                'schedule_start' => null,
                'schedule_end' => null,
                'workday' => 0,
                'work_hours' => 0,
                'real_hours' => 0,
                'late_minutes' => 0,
                'early_minutes' => 0,
                'status' => 'no_data',
                'status_text' => 'Chưa chấm công',
                'status_color' => 'default',
            ];
        }

        // Lấy thời gian ca làm việc (ưu tiên từ shift rule history)
        $scheduleStart = $shiftRule ? $shiftRule->start_time : $shift->start_time;
        $scheduleEnd = $shiftRule ? $shiftRule->end_time : $shift->end_time;

        // Tính toán các chỉ số
        $workday = $timesheet->calcWorkday(); // Số công
        $workHours = $timesheet->calcWorkDayHours(); // Số giờ làm việc
        $realHours = $timesheet->calcWorkDayRealHours(); // Số giờ thực tế



        // Tính toán đi muộn/về sớm
        $lateMinutes = 0;
        $earlyMinutes = 0;

        if ($timesheet->checkin && $scheduleStart) {
            $checkinTime = Carbon::parse($timesheet->checkin);
            $scheduleStartTime = Carbon::parse($timesheet->date->format('Y-m-d') . ' ' . $scheduleStart);

            if ($checkinTime->gt($scheduleStartTime)) {
                $lateMinutes = $checkinTime->diffInMinutes($scheduleStartTime);
            }
        }

        if ($timesheet->checkout && $scheduleEnd) {
            $checkoutTime = Carbon::parse($timesheet->checkout);
            $scheduleEndTime = Carbon::parse($timesheet->date->format('Y-m-d') . ' ' . $scheduleEnd);

            if ($checkoutTime->lt($scheduleEndTime)) {
                $earlyMinutes = $scheduleEndTime->diffInMinutes($checkoutTime);
            }
        }

        // Xác định trạng thái
        $status = $this->determineAttendanceStatus($workday, $lateMinutes, $earlyMinutes, $timesheet);

        // Note: explanations sẽ được xử lý ở level cao hơn

        return [
            'date' => $timesheet->date,
            'checkin' => $timesheet->checkin,
            'checkout' => $timesheet->checkout,
            'shift_name' => $shift->name,
            'schedule_start' => $scheduleStart,
            'schedule_end' => $scheduleEnd,
            'workday' => $workday,
            'work_hours' => $workHours,
            'real_hours' => $realHours,
            'late_minutes' => $lateMinutes,
            'early_minutes' => $earlyMinutes,
            'status' => $status,
            'status_text' => $this->getStatusText($status),
            'status_color' => $this->getStatusColor($status),
        ];
    }

    /**
     * Xác định trạng thái chấm công
     */
    protected function determineAttendanceStatus(float $workday, int $lateMinutes, int $earlyMinutes, Timesheet $timesheet): string
    {
        // Nếu không có checkin, checkout, đi muộn, về sớm, hoặc thiếu giờ
        if (!$timesheet->checkin || !$timesheet->checkout ||
            $lateMinutes > 0 || $earlyMinutes > 0 || $workday < 0.8) {
            return 'has_issue';
        }

        // Chấm công hoàn hảo
        return 'perfect';
    }

    /**
     * Lấy text mô tả trạng thái
     */
    protected function getStatusText(string $status): string
    {
        $statusTexts = [
            'perfect' => 'Hoàn hảo',
            'has_issue' => 'Có vấn đề',
            'no_data' => 'Chưa chấm công',
        ];

        return $statusTexts[$status] ?? 'Không xác định';
    }

    /**
     * Lấy màu sắc cho trạng thái
     */
    protected function getStatusColor(string $status): string
    {
        $statusColors = [
            'perfect' => 'success',    // Xanh lá
            'has_issue' => 'danger',   // Đỏ
            'no_data' => 'default',    // Xám
        ];

        return $statusColors[$status] ?? 'secondary';
    }

    /**
     * Tính toán số công cuối cùng sau khi áp dụng điều chỉnh từ giải trình
     */
    public function getFinalWorkday(Timesheet $timesheet, Collection $explanations): float
    {
        $originalWorkday = $timesheet->calcWorkday();

        // Lấy các giải trình đã được duyệt và ảnh hưởng đến workday
        $approvedExplanations = $explanations->filter(function($explanation) {
            return $explanation->affectsWorkday();
        });

        if ($approvedExplanations->isEmpty()) {
            return $originalWorkday;
        }

        // Áp dụng điều chỉnh - lấy giá trị cao nhất
        $adjustedWorkday = $originalWorkday;

        foreach ($approvedExplanations as $explanation) {
            $adjustment = $explanation->getAdjustmentData();
            if ($adjustment && $adjustment['workday_adjustment'] > 0) {
                $adjustedWorkday = max($adjustedWorkday, $adjustment['workday_adjustment']);
            }
        }

        return $adjustedWorkday;
    }

    /**
     * Tính toán tổng số giờ OT đã được duyệt
     */
    public function getFinalOtHours(Collection $explanations): float
    {
        return $explanations
            ->filter(function($explanation) {
                return $explanation->addsOtHours();
            })
            ->sum('ot_hours');
    }

    /**
     * Lấy lý do điều chỉnh từ các giải trình đã duyệt
     */
    protected function getAdjustmentReason(Collection $explanations): ?string
    {
        $approvedExplanations = $explanations->filter(function($explanation) {
            return $explanation->final_status === 'approved';
        });

        if ($approvedExplanations->isEmpty()) {
            return null;
        }

        return $approvedExplanations->map(function($explanation) {
            return $explanation->explanation_type_text . ': ' . $explanation->explanation;
        })->implode('; ');
    }

    /**
     * Tạo dữ liệu calendar cho API (chỉ ngày trong tháng)
     */
    public function generateCalendarDataForApi(Carbon $month, Collection $attendanceData): array
    {
        $startDate = $month->copy()->startOfMonth();
        $endDate = $month->copy()->endOfMonth();

        $calendar = [];
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $dateKey = $currentDate->format('Y-m-d');

            $dayData = [
                'date' => $currentDate->copy(),
                'day' => $currentDate->day,
                'is_current_month' => true, // Luôn true vì chỉ lấy ngày trong tháng
                'is_today' => $currentDate->isToday(),
                'is_weekend' => $currentDate->isWeekend(),
                'day_of_week' => $currentDate->dayOfWeek, // 0=Sunday, 1=Monday, ...
                'day_name' => $currentDate->format('l'), // Monday, Tuesday, ...
                'attendance' => null,
            ];

            // Thêm dữ liệu chấm công nếu có
            if ($attendanceData->has($dateKey)) {
                $dayData['attendance'] = $attendanceData->get($dateKey);
            }

            $calendar[] = $dayData;
            $currentDate->addDay();
        }

        return $calendar;
    }

    /**
     * Tạo dữ liệu calendar cho tháng (format cho web view)
     */
    public function generateCalendarData(Carbon $month, Collection $attendanceData): array
    {
        $startDate = $month->copy()->startOfMonth();
        $endDate = $month->copy()->endOfMonth();

        // Tìm ngày đầu tuần của tuần chứa ngày 1
        $calendarStart = $startDate->copy()->startOfWeek(Carbon::MONDAY);

        // Tìm ngày cuối tuần của tuần chứa ngày cuối tháng
        $calendarEnd = $endDate->copy()->endOfWeek(Carbon::SUNDAY);

        $calendar = [];
        $currentDate = $calendarStart->copy();

        while ($currentDate->lte($calendarEnd)) {
            $week = [];

            for ($i = 0; $i < 7; $i++) {
                $dateKey = $currentDate->format('Y-m-d');
                $isCurrentMonth = $currentDate->month === $month->month;

                $dayData = [
                    'date' => $currentDate->copy(),
                    'day' => $currentDate->day,
                    'is_current_month' => $isCurrentMonth,
                    'is_today' => $currentDate->isToday(),
                    'is_weekend' => $currentDate->isWeekend(),
                    'day_of_week' => $currentDate->dayOfWeek, // 0=Sunday, 1=Monday, ...
                    'day_name' => $currentDate->format('l'), // Monday, Tuesday, ...
                    'attendance' => null,
                ];

                // Thêm dữ liệu chấm công nếu có (cho tất cả ngày, không chỉ current month)
                if ($attendanceData->has($dateKey)) {
                    $dayData['attendance'] = $attendanceData->get($dateKey);
                }

                $week[] = $dayData;
                $currentDate->addDay();
            }

            $calendar[] = $week;
        }

        return $calendar;
    }

    /**
     * Lưu giải trình chấm công
     */
    public function saveExplanation(User $user, Carbon $date, string $explanation, string $explanationType, ?array $taggedUsers = null, ?int $approvedBy = null, ?float $otHours = null): bool
    {
        try {
            // Kiểm tra giới hạn 3 giải trình/tháng (trừ OT, remote work và other)
            if (!in_array($explanationType, ['overtime', 'remote_work', 'other'])) {
                $canCreate = $this->canCreateExplanation($user, $date);
                if (!$canCreate['allowed']) {
                    throw new \Exception($canCreate['message']);
                }
            }

            // Tìm manager mặc định nếu không có approved_by
            if (!$approvedBy) {
                $approvedBy = $this->getDefaultManager($user);
            }

            AttendanceExplanation::create([
                'user_id' => $user->id,
                'date' => $date->format('Y-m-d'),
                'explanation' => $explanation,
                'explanation_type' => $explanationType,
                'ot_hours' => $explanationType === 'overtime' ? $otHours : null,
                'created_by' => $user->id,
                'tagged_users' => $taggedUsers,
                'manager_status' => 'pending',
                'hr_status' => 'pending',
                'final_status' => 'pending',
            ]);

            return true;
        } catch (\Exception $e) {
            \Log::error('Error saving attendance explanation: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Kiểm tra có thể tạo giải trình không (giới hạn 3/tháng trừ OT)
     */
    public function canCreateExplanation(User $user, Carbon $date): array
    {
        $month = $date->format('Y-m');

        // Đếm số giải trình trong tháng (trừ OT, remote work, other, đã xóa, và đã tạm dừng)
        $count = AttendanceExplanation::where('user_id', $user->id)
            ->where('date', 'LIKE', $month . '%')
            ->whereNotIn('explanation_type', ['overtime', 'remote_work', 'other'])
            ->whereNull('deleted_at') // Chỉ đếm những cái chưa bị xóa
            ->where('final_status', '!=', 'paused') // Không đếm giải trình đã tạm dừng
            ->count();

        if ($count >= 3) {
            return [
                'allowed' => false,
                'message' => 'Bạn đã tạo đủ 3 giải trình trong tháng này (không bao gồm OT, remote work và lý do khác). Vui lòng xóa hoặc tạm dừng giải trình cũ nếu muốn tạo mới.',
                'current_count' => $count,
                'max_count' => 3
            ];
        }

        return [
            'allowed' => true,
            'message' => 'Có thể tạo giải trình',
            'current_count' => $count,
            'max_count' => 3
        ];
    }

    /**
     * Lấy thống kê giải trình trong tháng
     */
    public function getMonthlyExplanationStats(User $user, Carbon $date): array
    {
        $month = $date->format('Y-m');

        // Đếm giải trình thường (trừ OT, remote work, other, đã xóa, và đã tạm dừng)
        $activeCount = AttendanceExplanation::where('user_id', $user->id)
            ->where('date', 'LIKE', $month . '%')
            ->whereNotIn('explanation_type', ['overtime', 'remote_work', 'other'])
            ->whereNull('deleted_at')
            ->where('final_status', '!=', 'paused')
            ->count();

        // Đếm giải trình đã tạm dừng
        $pausedCount = AttendanceExplanation::where('user_id', $user->id)
            ->where('date', 'LIKE', $month . '%')
            ->whereNotIn('explanation_type', ['overtime', 'remote_work', 'other'])
            ->whereNull('deleted_at')
            ->where('final_status', 'paused')
            ->count();

        // Đếm giải trình OT
        $otCount = AttendanceExplanation::where('user_id', $user->id)
            ->where('date', 'LIKE', $month . '%')
            ->where('explanation_type', 'overtime')
            ->whereNull('deleted_at')
            ->count();

        return [
            'regular_count' => $activeCount,
            'paused_count' => $pausedCount,
            'ot_count' => $otCount,
            'remaining' => max(0, 3 - $activeCount),
            'can_create' => $activeCount < 3
        ];
    }

    /**
     * Lấy manager mặc định của user
     */
    protected function getDefaultManager(User $user): ?int
    {
        // Tìm trong bảng user_managers
        $manager = \DB::table('user_managers')
            ->where('user_id', $user->id)
            ->whereNull('deleted_at')
            ->first();

        return $manager ? $manager->manager_user_id : null;
    }

    /**
     * Lấy danh sách members mà manager đang quản lý
     */
    public function getManagedMembers(User $manager): \Illuminate\Support\Collection
    {
        $memberIds = \DB::table('user_managers')
            ->where('manager_user_id', $manager->id)
            ->whereNull('deleted_at')
            ->pluck('user_id');

        return User::whereIn('id', $memberIds)
            ->where('active_flag', true)
            ->whereNull('deleted_at')
            ->with(['staffDepartment'])
            ->select('id', 'name', 'phone_number', 'account', 'staff_department_id')
            ->orderBy('name')
            ->get();
    }

    /**
     * Kiểm tra quyền manager duyệt
     */
    public function canManagerApprove(User $user, AttendanceExplanation $explanation): bool
    {
        // Kiểm tra user có phải manager của người tạo giải trình không
        $isManager = \DB::table('user_managers')
            ->where('user_id', $explanation->user_id)
            ->where('manager_user_id', $user->id)
            ->whereNull('deleted_at')
            ->exists();

        // Kiểm tra tagged user đã confirm chưa (nếu có)
        $taggedUserConfirmed = true; // Mặc định là true nếu không có tagged user
        if ($explanation->tagged_user_id) {
            $taggedUserConfirmed = $explanation->tagged_user_status === 'confirmed';
        }

        return $isManager &&
               $explanation->manager_status === 'pending' &&
               $taggedUserConfirmed;
    }

    /**
     * Kiểm tra quyền HR duyệt (chỉ role R080)
     */
    public function canHrApprove(User $user, AttendanceExplanation $explanation): bool
    {
        // Chỉ user có role R080 mới được duyệt HR
        $isR080 = $user->hasRole('R080');

        // Manager phải đã duyệt và HR chưa duyệt
        $canApprove = $explanation->manager_status === 'approved' && $explanation->hr_status === 'pending';

        return $isR080 && $canApprove;
    }

    /**
     * Xóa giải trình (soft delete)
     */
    public function deleteExplanation(User $user, AttendanceExplanation $explanation): array
    {
        try {
            // Kiểm tra quyền xóa (chỉ người tạo và chưa được duyệt hoàn toàn)
            if ($explanation->user_id !== $user->id) {
                return [
                    'success' => false,
                    'message' => 'Bạn không có quyền xóa giải trình này'
                ];
            }

            if ($explanation->final_status === 'approved') {
                return [
                    'success' => false,
                    'message' => 'Không thể xóa giải trình đã được duyệt hoàn toàn'
                ];
            }

            if ($explanation->manager_status === 'approved') {
                return [
                    'success' => false,
                    'message' => 'Không thể xóa giải trình đã được manager phê duyệt'
                ];
            }

            $explanation->delete(); // Soft delete

            return [
                'success' => true,
                'message' => 'Đã xóa giải trình thành công'
            ];
        } catch (\Exception $e) {
            \Log::error('Error deleting attendance explanation: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa giải trình'
            ];
        }
    }

    /**
     * Tạm dừng giải trình
     */
    public function pauseExplanation(User $user, AttendanceExplanation $explanation): array
    {
        try {
            // Kiểm tra quyền tạm dừng (chỉ người tạo và đang pending)
            if ($explanation->user_id !== $user->id) {
                return [
                    'success' => false,
                    'message' => 'Bạn không có quyền tạm dừng giải trình này'
                ];
            }

            if ($explanation->final_status !== 'pending') {
                return [
                    'success' => false,
                    'message' => 'Chỉ có thể tạm dừng giải trình đang chờ duyệt'
                ];
            }

            if ($explanation->manager_status === 'approved') {
                return [
                    'success' => false,
                    'message' => 'Không thể tạm dừng giải trình đã được manager phê duyệt'
                ];
            }

            $explanation->update([
                'final_status' => 'paused',
                'manager_status' => 'paused',
                'hr_status' => 'paused'
            ]);

            return [
                'success' => true,
                'message' => 'Đã tạm dừng giải trình thành công'
            ];
        } catch (\Exception $e) {
            \Log::error('Error pausing attendance explanation: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạm dừng giải trình'
            ];
        }
    }

    /**
     * Tiếp tục giải trình đã tạm dừng
     */
    public function resumeExplanation(User $user, AttendanceExplanation $explanation): array
    {
        try {
            // Kiểm tra quyền tiếp tục (chỉ người tạo và đang paused)
            if ($explanation->user_id !== $user->id) {
                return [
                    'success' => false,
                    'message' => 'Bạn không có quyền tiếp tục giải trình này'
                ];
            }

            if ($explanation->final_status !== 'paused') {
                return [
                    'success' => false,
                    'message' => 'Chỉ có thể tiếp tục giải trình đang tạm dừng'
                ];
            }

            // Kiểm tra giới hạn trước khi tiếp tục (trừ OT, remote work và other)
            if (!in_array($explanation->explanation_type, ['overtime', 'remote_work', 'other'])) {
                $canCreate = $this->canCreateExplanation($user, \Carbon\Carbon::parse($explanation->date));
                if (!$canCreate['allowed']) {
                    return [
                        'success' => false,
                        'message' => 'Không thể tiếp tục giải trình vì đã đạt giới hạn 3 giải trình/tháng. Vui lòng xóa hoặc tạm dừng giải trình khác trước.'
                    ];
                }
            }

            $explanation->update([
                'final_status' => 'pending',
                'manager_status' => 'pending',
                'hr_status' => 'pending'
            ]);

            return [
                'success' => true,
                'message' => 'Đã tiếp tục giải trình thành công'
            ];
        } catch (\Exception $e) {
            \Log::error('Error resuming attendance explanation: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tiếp tục giải trình'
            ];
        }
    }

    /**
     * Kiểm tra có thể xóa giải trình không
     */
    public function canDeleteExplanation(User $user, AttendanceExplanation $explanation): array
    {
        // Kiểm tra quyền sở hữu
        if ($explanation->user_id !== $user->id) {
            return [
                'allowed' => false,
                'message' => 'Bạn không có quyền xóa giải trình này'
            ];
        }

        // Kiểm tra trạng thái final
        if ($explanation->final_status === 'approved') {
            return [
                'allowed' => false,
                'message' => 'Không thể xóa giải trình đã được duyệt hoàn tất'
            ];
        }

        // Kiểm tra trạng thái manager
        if ($explanation->manager_status === 'approved') {
            return [
                'allowed' => false,
                'message' => 'Không thể xóa giải trình đã được manager duyệt'
            ];
        }

        return [
            'allowed' => true,
            'message' => 'Có thể xóa giải trình'
        ];
    }

    /**
     * Kiểm tra có thể tạm dừng giải trình không
     */
    public function canPauseExplanation(User $user, AttendanceExplanation $explanation): bool
    {
        return $explanation->user_id === $user->id &&
               $explanation->final_status === 'pending' &&
               $explanation->manager_status !== 'approved';
    }

    /**
     * Kiểm tra có thể tiếp tục giải trình không
     */
    public function canResumeExplanation(User $user, AttendanceExplanation $explanation): bool
    {
        return $explanation->user_id === $user->id && $explanation->final_status === 'paused';
    }

    /**
     * Kiểm tra có thể cập nhật giải trình không (chưa có ai duyệt)
     */
    public function canUpdateExplanation(User $user, AttendanceExplanation $explanation): array
    {
        // Chỉ người tạo mới có thể update
        if ($explanation->user_id !== $user->id) {
            return [
                'allowed' => false,
                'message' => 'Bạn không có quyền cập nhật giải trình này'
            ];
        }

        // Kiểm tra tagged user đã confirm chưa
        if ($explanation->tagged_user_id && $explanation->tagged_user_status !== 'pending') {
            return [
                'allowed' => false,
                'message' => 'Không thể cập nhật vì user được tag đã xử lý giải trình này'
            ];
        }

        // Kiểm tra manager đã duyệt chưa
        if ($explanation->manager_status !== 'pending') {
            return [
                'allowed' => false,
                'message' => 'Không thể cập nhật vì manager đã xử lý giải trình này'
            ];
        }

        // Kiểm tra HR đã duyệt chưa
        if ($explanation->hr_status !== 'pending') {
            return [
                'allowed' => false,
                'message' => 'Không thể cập nhật vì HR đã xử lý giải trình này'
            ];
        }

        // Chỉ cho phép update khi trạng thái là pending_tagged_user, pending, hoặc paused
        $allowedStatuses = ['pending_tagged_user', 'pending', 'paused'];
        if (!in_array($explanation->final_status, $allowedStatuses)) {
            return [
                'allowed' => false,
                'message' => 'Không thể cập nhật giải trình ở trạng thái hiện tại'
            ];
        }

        return [
            'allowed' => true,
            'message' => 'Có thể cập nhật giải trình'
        ];
    }

    /**
     * Cập nhật giải trình chấm công
     */
    public function updateExplanation(User $user, AttendanceExplanation $explanation, array $data): array
    {
        try {
            // Lưu tagged_user_id cũ để so sánh
            $oldTaggedUserId = $explanation->tagged_user_id;
            $newTaggedUserId = $data['tagged_user_id'] ?? null;

            // Cập nhật thông tin cơ bản
            $updateData = [
                'explanation' => $data['explanation'],
                'explanation_type' => $data['explanation_type'],
                'ot_hours' => $data['ot_hours'] ?? null,
                'remote_shift_id' => $data['remote_shift_id'] ?? null,
                'tagged_user_id' => $newTaggedUserId,
            ];

            // Nếu thay đổi tagged user, reset trạng thái tagged user
            if ($oldTaggedUserId != $newTaggedUserId) {
                if ($newTaggedUserId) {
                    // Có tagged user mới
                    $updateData['tagged_user_status'] = 'pending';
                    $updateData['tagged_user_confirmed_at'] = null;
                    $updateData['tagged_user_note'] = null;
                } else {
                    // Không có tagged user
                    $updateData['tagged_user_status'] = null;
                    $updateData['tagged_user_confirmed_at'] = null;
                    $updateData['tagged_user_note'] = null;
                }
            }

            $explanation->update($updateData);

            // Cập nhật final status
            $explanation->updateFinalStatus();

            // Gửi notification cho tagged user mới (nếu có thay đổi)
            if ($oldTaggedUserId != $newTaggedUserId && $newTaggedUserId) {
                $taggedUser = \App\Models\User::find($newTaggedUserId);
                if ($taggedUser) {
                    $taggedUser->notify(new \App\Notifications\AttendanceExplanationTaggedNotification($explanation, $user));
                }
            }

            return [
                'success' => true,
                'message' => 'Đã cập nhật giải trình thành công'
            ];
        } catch (\Exception $e) {
            \Log::error('Error updating explanation: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật giải trình'
            ];
        }
    }

    /**
     * Manager duyệt giải trình với response format (cho API)
     */
    public function managerApproveExplanation(User $manager, AttendanceExplanation $explanation, bool $approve, ?string $note = null): array
    {
        try {
            // Kiểm tra quyền duyệt
            if (!$this->canManagerApprove($manager, $explanation)) {
                return [
                    'success' => false,
                    'message' => 'Bạn không có quyền duyệt giải trình này'
                ];
            }

            // Kiểm tra trạng thái
            if ($explanation->manager_status !== 'pending') {
                return [
                    'success' => false,
                    'message' => 'Giải trình này đã được xử lý trước đó'
                ];
            }

            $status = $approve ? 'approved' : 'rejected';
            $success = $this->managerApprove($explanation, $manager, $status, $note);

            if ($success) {
                return [
                    'success' => true,
                    'message' => $approve ? 'Duyệt giải trình thành công' : 'Từ chối giải trình thành công'
                ];
            }

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xử lý giải trình'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Manager duyệt giải trình (Bước 1)
     */
    public function managerApprove(AttendanceExplanation $explanation, User $manager, string $status, ?string $note = null): bool
    {
        try {
            $explanation->update([
                'manager_status' => $status,
                'manager_approved_by' => $manager->id,
                'manager_approved_at' => now(),
                'manager_note' => $note,
            ]);

            // Cập nhật final_status
            $explanation->updateFinalStatus();

            return true;
        } catch (\Exception $e) {
            \Log::error('Error in manager approve: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * HR duyệt giải trình với response format (cho API)
     */
    public function hrApproveExplanation(User $hr, AttendanceExplanation $explanation, bool $approve, ?string $note = null): array
    {
        try {
            // Kiểm tra quyền duyệt
            if (!$this->canHrApprove($hr, $explanation)) {
                return [
                    'success' => false,
                    'message' => 'Bạn không có quyền duyệt giải trình này hoặc manager chưa duyệt'
                ];
            }

            // Kiểm tra trạng thái
            if ($explanation->hr_status !== 'pending') {
                return [
                    'success' => false,
                    'message' => 'Giải trình này đã được HR xử lý trước đó'
                ];
            }

            $status = $approve ? 'approved' : 'rejected';
            $success = $this->hrApprove($explanation, $hr, $status, $note);

            if ($success) {
                return [
                    'success' => true,
                    'message' => $approve ? 'HR duyệt giải trình thành công' : 'HR từ chối giải trình thành công'
                ];
            }

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xử lý giải trình'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ];
        }
    }

    /**
     * HR duyệt giải trình (Bước 2)
     */
    public function hrApprove(AttendanceExplanation $explanation, User $hr, string $status, ?string $note = null): bool
    {
        try {
            $explanation->update([
                'hr_status' => $status,
                'hr_approved_by' => $hr->id,
                'hr_approved_at' => now(),
                'hr_note' => $note,
            ]);

            // Cập nhật final_status
            $explanation->updateFinalStatus();

            return true;
        } catch (\Exception $e) {
            \Log::error('Error in HR approve: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Lấy danh sách đồng nghiệp cùng team để tag
     */
    public function getColleagues(User $user)
    {
        // Lấy danh sách user cùng department
        $colleagues = User::where('id', '!=', $user->id)
            ->where('active_flag', true)
            ->whereNull('deleted_at')
            ->with(['staffDepartment'])
            ->orderBy('name')
            ->get();

        return $colleagues;
    }
}
