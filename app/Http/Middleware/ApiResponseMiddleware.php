<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class ApiResponseMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only process JSON responses for API routes
        if ($request->is('api/*') && $response instanceof JsonResponse) {
            $data = $response->getData(true);
            
            // If response doesn't have standard format, wrap it
            if (!isset($data['success'])) {
                $statusCode = $response->getStatusCode();
                
                if ($statusCode >= 200 && $statusCode < 300) {
                    // Success response
                    $formattedData = [
                        'success' => true,
                        'data' => $data,
                        'message' => 'Request successful',
                        'timestamp' => now()->toISOString(),
                        'status_code' => $statusCode
                    ];
                } else {
                    // Error response
                    $formattedData = [
                        'success' => false,
                        'data' => null,
                        'message' => $data['message'] ?? 'An error occurred',
                        'errors' => $data['errors'] ?? null,
                        'timestamp' => now()->toISOString(),
                        'status_code' => $statusCode
                    ];
                }

                $response->setData($formattedData);
            } else {
                // Add timestamp and status_code to existing format
                $data['timestamp'] = now()->toISOString();
                $data['status_code'] = $response->getStatusCode();
                $response->setData($data);
            }
        }

        return $response;
    }
}
