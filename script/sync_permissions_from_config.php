<?php

/**
 * <PERSON>ript to sync permissions from config/common_permission.php to database
 *
 * Usage:
 * php script/sync_permissions_from_config.php
 *
 * Or in Docker:
 * docker-compose exec lavian_php-fpm php script/sync_permissions_from_config.php
 */

// Bootstrap Laravel application
require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Syncing permissions from config/common_permission.php...\n";

// Clear permission cache
app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

// Load permissions from config
$commonPermissions = config('common_permission.role_permission');

if (empty($commonPermissions)) {
    echo "Error: No role_permission found in config/common_permission.php\n";
    exit(1);
}

echo "Found " . count($commonPermissions) . " role configurations...\n";

// Sync permissions for each role
foreach ($commonPermissions as $roleEnum => $permissions) {
    // Find role by enum value
    $role = \Spatie\Permission\Models\Role::where('name', $roleEnum)->first();
    
    if (!$role) {
        echo "Warning: Role {$roleEnum} not found in database, skipping...\n";
        continue;
    }
    
    echo "Syncing role: {$roleEnum} ({$role->display_name})\n";
    
    if (empty($permissions)) {
        echo "  No permissions defined for this role\n";
        $role->syncPermissions([]);
        continue;
    }
    
    echo "  Assigning " . count($permissions) . " permissions...\n";
    
    // Create any missing permissions
    foreach ($permissions as $permissionName) {
        $permission = \Spatie\Permission\Models\Permission::where('name', $permissionName)->first();
        
        if (!$permission) {
            echo "  Creating new permission: {$permissionName}\n";
            $permission = \Spatie\Permission\Models\Permission::create([
                'name' => $permissionName,
                'guard_name' => 'web'
            ]);
        }
    }
    
    // Sync permissions to role
    $role->syncPermissions($permissions);
    
    echo "  Successfully synced " . count($permissions) . " permissions\n";
}

// Clear permission cache again
app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

echo "Sync completed successfully!\n";
echo "Run 'php script/export_permissions.php' to verify the changes.\n";
